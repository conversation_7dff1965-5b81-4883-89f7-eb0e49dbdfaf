<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\ResetPasswordRequest;
use App\Http\Requests\RegisterUserRequest;
use App\Http\Requests\SendOtpRequest;
use App\Http\Requests\SocialProviderRequest;
use App\Logics\UserManager;
use Illuminate\Http\Request;
use App\Models\UserDevice;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Tymon\JWTAuth\Facades\JWTAuth;
use Dedoc\Scramble\Attributes\Parameter;

class AuthController extends Controller
{
    private $userManager;
    public function __construct(UserManager $userManager,)
    {
        $this->userManager = $userManager;
    }
    /**
     * Login
     * @response array{"code": 200, "message": "Thành công", "data": App\Http\Resources\LoginResource}
     * @unauthenticated
     */
    public function login(LoginRequest $request)
    {
        $userName = $request->input('email');
        $loginType = filter_var($userName, FILTER_VALIDATE_EMAIL) ? 'email' : 'phone';

        $credentials = [
            $loginType => $userName,
            'password' => $request->input('password')
        ];
        if (! $token = auth('api')->attempt($credentials)) {
            return response()->json(
                [
                    'code' => Response::HTTP_FORBIDDEN,
                    'message' => trans('message.error_login')
                ],
                Response::HTTP_FORBIDDEN
            );
        }
        $user = auth('api')->user();
        // get token and user info
        $data = $this->userManager->generateToken($user, $request);

        return response()->json([
            'code' => Response::HTTP_OK,
            'message' => __('language.success'),
            'data' => $data
        ], Response::HTTP_OK);
    }

    /**
     * Logout
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    #[Parameter('body','device_token', 'Device token', example: 'eyJhbGciOiJSUzI1NiIsImtpZCI6IjA3ZjA3OGYyNjQ...')]
    public function logout(Request $request)
    {
        //Logout
        auth('api')->logout();
        //If has param device_token then remove UserDevice.
        if ($request->filled('device_token')) {
            $token = $request->input('device_token');
            UserDevice::where('user_id', Auth::id())
                ->where('device_token', $token)
                ->delete();
        }
        return response()->json(['message' => __('message.logouted')]);
    }

    public function refresh(Request $request)
    {
        $refreshToken = $request->input('refresh_token') ?? $request->bearerToken();

        try {
            $payload = JWTAuth::setToken($refreshToken)->getPayload();
            if (!$payload->get('refresh')) {
                return response()->json(['error' => 'Invalid refresh token'], 401);
            }
            // Lấy user từ refresh token
            $user = JWTAuth::setToken($refreshToken)->toUser();
            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }
            // Tạo access token mới với guard api
            $accessToken = auth('api')->login($user);
            // Tạo refresh token mới
            $newRefreshToken = JWTAuth::claims(['refresh' => true])->fromUser($user);

            return response()->json([
                'access_token' => $accessToken,
                'refresh_token' => $newRefreshToken,
                'token_type' => 'bearer',
                'expires_in' => auth('api')->factory()->getTTL() * 60
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Invalid token'], 401);
        }
    }

    /**
     * Login social
     *
     * @param SocialProviderRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function loginSocial(SocialProviderRequest $request)
    {
        $data = $request->validated();
        $result = $this->userManager->loginWithSocial($data);
        if (!$result) {
            return response()->json([
                'status'  => false,
                'message' => trans('message.login_failed')
            ], Response::HTTP_UNAUTHORIZED);
        }
        return response()->json([
            'status'  => true,
            'message' => trans('message.login_success'),
            'data'    => $result
        ], Response::HTTP_OK);
    }

    /**
     * Handle send code to verify.
     * @param SendOtpRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendCode(SendOtpRequest $request)
    {
        return $this->userManager->handleSendCode($request);
    }

    /**
     * Verify code password
     * @param SendOtpRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyCode(SendOtpRequest $request)
    {
        return $this->userManager->handleVerifyCode($request);
    }

    /**
     * Reset password
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetPassword(ResetPasswordRequest $request)
    {
        return $this->userManager->handleResetPassword($request);
    }
    /**
     * Register user
     *
     * Api register user
     * @param RegisterUserRequest $request
     * @response array{"message": "Thành công", "data": App\Http\Resources\LoginResource}
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(RegisterUserRequest $request)
    {
        $result = $this->userManager->handleRegister($request);

        return response()->json([
            'message' => __('language.success'),
            'data' => $result,
        ]);
    }
}
