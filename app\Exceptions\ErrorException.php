<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ErrorException extends Exception
{
    protected int $status;
    protected array|string|null $errors;

    public function __construct(
        string $message = "Something went wrong",
        int $status = Response::HTTP_BAD_REQUEST,
        array|string|null $errors = null
    ) {
        parent::__construct($message);
        $this->status = $status;
        $this->errors = $errors;
    }

    /**
     * Handle errors exception
     *
     * @param Request $request
     * @return JsonResponse|RedirectResponse
     */
    public function render(Request $request): JsonResponse | RedirectResponse
    {
        $errors = [
            'message' => $this->getMessage(),
            'errors'  => $this->errors,
        ];
        if ($request->expectsJson()) {
            return response()->json($errors, $this->status);
        }
        return redirect()->back()->with($errors);
    }
}
