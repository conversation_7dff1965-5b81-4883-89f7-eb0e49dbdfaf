<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plots', function (Blueprint $t) {
            $t->id();
            $t->unsignedBigInteger('land_id');
            $t->unsignedInteger('x');
            $t->unsignedInteger('y');
            $t->timestamps();
            $t->softDeletes();

            $t->unique(['land_id', 'x', 'y']);
            $t->index('land_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plots');
    }
};
