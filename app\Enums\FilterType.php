<?php

namespace App\Enums;

/**
 * Simple enum to manage field types in FilterHelper
 */
enum FilterType: string
{
    case TEXT = 'text';
    case IDS = 'ids';
    case ARRAY = 'array';
    case SELECT = 'select';
    case BOOLEAN = 'boolean';
    case DATE = 'date';
    case RANGE = 'range';
    case FIELD_FROM_DATABASE = 'field_from_database';

    /**
     * Get all available types
     */
    public static function getAllTypes(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    /**
     * Check if type is valid
     */
    public static function isValid(string $type): bool
    {
        return in_array($type, self::getAllTypes());
    }

    /**
     * Create field config quickly with enum
     */
    public static function createConfig(string $field, FilterType $type, array $options = []): array
    {
        return [
            'field' => $field,
            'type' => $type->value,
            'options' => $options
        ];
    }
}
