<?php

namespace Database\Seeders;

use App\Models\CropGroup;
use App\Models\CropGroupLocale;
use App\Models\Language;
use Illuminate\Database\Seeder;

class CropGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define multilingual data - easy to add new languages
        $cropGroupsData = [
            [
                Language::VI => 'Rau ăn lá',
                // Language::EN => 'Leafy Vegetables', // Uncomment to add English
            ],
            [
                Language::VI => 'Rau ăn quả',
                // Language::EN => 'Fruit Vegetables',
            ],
            [
                Language::VI => 'Rau ăn thân',
                // Language::EN => 'Stem Vegetables',
            ],
            [
                Language::VI => 'Rau ăn củ',
                // Language::EN => 'Root Vegetables',
            ],
            [
                Language::VI => 'Rau ăn hoa',
                // Language::EN => 'Flower Vegetables',
            ],
            [
                Language::VI => 'Rau gia vị',
                // Language::EN => 'Herb Vegetables',
            ],
            [
                Language::VI => 'Rau mầm',
                // Language::EN => 'Sprout Vegetables',
            ],
            [
                Language::VI => 'Cây hoa màu',
                // Language::EN => 'Field Crops',
            ],
            [
                Language::VI => 'Cây dược liệu',
                // Language::EN => 'Medicinal Plants',
            ],
            [
                Language::VI => 'Cây ăn quả ngắn ngày',
                // Language::EN => 'Short-term Fruit Trees',
            ],
        ];

        // Extract all language codes from the data
        $languageCodes = collect($cropGroupsData)
            ->flatMap(fn($translations) => array_keys($translations))
            ->unique()
            ->values()
            ->toArray();

        $languages = Language::whereIn('code', $languageCodes)->get()->keyBy('code');

        // Get all existing CropGroups ordered by id
        $existingGroups = CropGroup::orderBy('id')->get();

        foreach ($cropGroupsData as $index => $translations) {
            // Get existing group or create new one
            $group = $existingGroups->get($index) ?? CropGroup::create();

            // Create or update locale for each language
            foreach ($translations as $languageCode => $name) {
                $language = $languages->get($languageCode);

                if (!$language) {
                    continue; // Skip if language not found
                }

                CropGroupLocale::updateOrCreate(
                    [
                        'crop_group_id' => $group->id,
                        'language_id' => $language->id,
                    ],
                    [
                        'name' => $name
                    ]
                );
            }
        }
    }
}
