<?php

namespace App\Docs;

use Dedoc\Scramble\Extensions\OperationExtension;
use Dedoc\Scramble\Support\Generator\Operation;
use Dedoc\Scramble\Support\RouteInfo;

class PaginatedExtension extends OperationExtension
{
    /**
     * Custom docs resposne for pagination.
     *
     * @param Operation $operation
     * @param RouteInfo $routeInfo
     * @return void
     */
    public function handle(Operation $operation, RouteInfo $routeInfo): void
    {
        $paginated = $routeInfo->phpDoc()->getTagsByName('@paginated');
        $messageTag = $routeInfo->phpDoc()->getTagsByName('@message');

        //check use tag @paginated
        if (! $paginated) {
            return;
        }

        //default msg.
        $message = "Lấy danh sách thành công";

        if ($messageTag) {
            //get message from tag.
            [$messageTag] = array_values($messageTag);
            $message = $messageTag->value->value;
        }

        [$pagination] = array_values($paginated);

        //get pagination params from tag.
        $pagination = json_decode($pagination->value->value ?? '{}', true);

        foreach ($operation->responses as $response) {

            if (!empty($response->code) && $response->code >= 300) {
                //just for status ok.
                return;
            }
            $schema = $response->content['application/json'] ?? null;

            //convert schema to array.
            if ($schema && method_exists($schema, 'toArray')) {
                $schema = $schema->toArray();
            }
            //format base pagination response schema.
            $wrapper = [
                'type' => 'object',
                'properties' => [
                    'message' => [
                        'example' => $message,
                        'type' => 'string'
                    ],
                    'total' => [
                        'example' => $pagination['total'] ?? 1000,
                        'type' => 'integer',
                        'description' => 'Total items',
                    ],
                    'per_page' => [
                        'example' => $pagination['per_page'] ?? PER_PAGE,
                        'type' => 'integer',
                        'description' => 'Items per page',
                    ],
                    'current_page' => [
                        'example' => $pagination['current_page'] ?? 1,
                        'type' => 'integer',
                        'description' => 'Current page',
                    ],
                    'total_page' => [
                        'example' => $pagination['total_page'] ?? 67,
                        'type' => 'integer',
                        'description' => 'Total count pages.',
                    ],
                    'data' => $schema,
                ],
            ];
            //replace response old.
            $response->content['application/json'] = collect($wrapper);
        }
    }
}
