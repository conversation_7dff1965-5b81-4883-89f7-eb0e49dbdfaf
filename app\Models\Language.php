<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Language extends Model
{
    const EN = 'en';
    const VI = 'vi';
    const JA = 'ja';


    const ACTIVE = 1;
    const INACTIVE = 0;

    protected $table = 'languages';

    protected $fillable = ['code', 'name', 'display_name', 'flag', 'is_active'];

    /**
     * Get the crop locales for the language
     * @return HasMany
     */
    public function cropLocales(): HasMany
    {
        return $this->hasMany(CropLocale::class);
    }

    /**
     * Get the crop group locales for the language.
     * @return HasMany
     */
    public function cropGroupLocales(): HasMany
    {
        return $this->hasMany(CropGroupLocale::class);
    }

    /**
     * Scope active languages
     * @param $query
     * @return mixed
     */
    public function scopeActive($query): mixed
    {
        return $query->where('is_active', self::ACTIVE);
    }

    /**
     * Scope by code
     * @param $query
     * @param $code
     * @return mixed
     */
    public function scopeByCode($query, $code): mixed
    {
        return $query->where('code', $code);
    }
}
