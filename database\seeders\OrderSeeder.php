<?php

namespace Database\Seeders;

use App\Enums\OrderStatus;
use App\Enums\OrderType;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting to seed orders...');

        // Ensure we have users to assign orders to
        $userIds = User::pluck('id')->toArray();
        if (empty($userIds)) {
            $this->command->warn('No users found. Creating some users first...');
            User::factory(50)->create();
            $userIds = User::pluck('id')->toArray();
        }

        // Define distribution of order statuses
        $statusDistribution = [
            OrderStatus::PAID->value => 600,      // 60% paid orders
            OrderStatus::PENDING->value => 200,   // 20% pending orders
            OrderStatus::FAILED->value => 100,    // 10% failed orders
            OrderStatus::EXPIRED->value => 50,    // 5% expired orders
            OrderStatus::CANCELED->value => 30,   // 3% canceled orders
            OrderStatus::PROCESSING->value => 20, // 2% processing orders
        ];

        // Define distribution of order types
        $typeDistribution = [
            OrderType::ORDER_PAYMENT->value => 700,   // 70% order payments
            OrderType::RECHARGE_COINS->value => 300,  // 30% coin recharges
        ];

        $orders = [];
        $orderId = 1;

        // Create orders with different statuses
        foreach ($statusDistribution as $status => $count) {
            $this->command->info("Creating {$count} orders with status: {$status}");

            for ($i = 0; $i < $count; $i++) {
                // Determine order type based on distribution
                $orderType = $orderId <= 700
                    ? OrderType::ORDER_PAYMENT->value
                    : OrderType::RECHARGE_COINS->value;

                $amount = fake()->numberBetween(50000, 5000000);
                $createdAt = fake()->dateTimeBetween('-6 months', 'now');

                // Generate order name based on type
                $name = match($orderType) {
                    OrderType::RECHARGE_COINS->value => 'Nạp ' . number_format($amount / 1000) . ' xu',
                    OrderType::ORDER_PAYMENT->value => 'Thanh toán đơn hàng #' . fake()->randomNumber(6),
                    default => 'Đơn hàng #' . fake()->randomNumber(6),
                };

                // Meta data based on order type
                $meta = match($orderType) {
                    OrderType::RECHARGE_COINS->value => [
                        'coins' => $amount / 1000,
                        'bonus_coins' => fake()->numberBetween(0, $amount / 10000),
                        'package_name' => 'Gói nạp ' . number_format($amount / 1000) . ' xu'
                    ],
                    OrderType::ORDER_PAYMENT->value => [
                        'items' => [
                            [
                                'name' => fake()->words(3, true),
                                'quantity' => fake()->numberBetween(1, 5),
                                'price' => fake()->numberBetween(10000, 100000),
                            ]
                        ],
                        'shipping_address' => fake()->address(),
                        'note' => fake()->optional()->sentence(),
                    ],
                    default => []
                };

                // Set paid_at for paid orders
                $paidAt = null;
                if ($status === OrderStatus::PAID->value) {
                    $paidAt = fake()->dateTimeBetween($createdAt, 'now');
                }

                $orders[] = [
                    'id' => fake()->uuid(),
                    'user_id' => fake()->randomElement($userIds),
                    'type' => $orderType,
                    'code' => $this->generateOrderCode(),
                    'name' => $name,
                    'amount' => $amount,
                    'currency' => 'VND',
                    'status' => $status,
                    'meta' => json_encode($meta),
                    'paid_at' => $paidAt,
                    'latest_payment_id' => null, // Will be set after payments are created
                    'created_at' => $createdAt,
                    'updated_at' => fake()->dateTimeBetween($createdAt, 'now'),
                ];

                $orderId++;

                // Insert in batches of 100 to avoid memory issues
                if (count($orders) >= 100) {
                    DB::table('orders')->insert($orders);
                    $orders = [];
                    $this->command->info("Inserted batch of orders. Progress: {$orderId}/1000");
                }
            }
        }

        // Insert remaining orders
        if (!empty($orders)) {
            DB::table('orders')->insert($orders);
        }

        $this->command->info('Successfully created 1000 orders!');
    }

    /**
     * Generate unique order code
     */
    private function generateOrderCode(): string
    {
        do {
            $date = now()->format('ymd');
            $random = strtoupper(fake()->bothify('########'));
            $code = 'ORD' . $date . $random;
        } while (DB::table('orders')->where('code', $code)->exists());

        return $code;
    }
}
