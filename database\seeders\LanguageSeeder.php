<?php

namespace Database\Seeders;

use App\Models\Language;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $languages = [
            ['code' => Language::VI, 'name' => 'vi', 'display_name' => 'Tiếng Việt', 'flag' => '/images/flags/vi.webp', 'is_active' => true],
            ['code' => Language::EN, 'name' => 'en', 'display_name' => 'English', 'flag' => '/images/flags/en.webp', 'is_active' => false],
            ['code' => Language::JA, 'name' => 'ja', 'display_name' => '日本語', 'flag' => '/images/flags/jp.webp', 'is_active' => false],
        ];

        foreach ($languages as $language) {
            Language::updateOrCreate(['code' => $language['code']],$language);
        }
    }
}
