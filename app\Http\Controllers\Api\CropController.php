<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Logics\CropManager;
use Illuminate\Http\Request;

class CropController extends Controller
{
    protected $cropManager;

    /**
     * Constructor
     * @param CropManager $cropManager
     */
    public function __construct(CropManager $cropManager)
    {
        $this->cropManager = $cropManager;
    }

    /**
     * List crops
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @response App\Http\Resources\CropResource[]
     * @paginated
     */
    public function list(Request $request)
    {
        //Todo : change code validate after
        $request->validate([
            /**
             * Comma separated list of crop IDs.
             * @example 1,2,3
             */
            'ids' => ['nullable', 'string'],
            /**
             * Crop group ID.
             * @example 1
             */
            'group_id' => ['nullable', 'integer'],
            /**
             * Search keyword.
             * @example Rau muong
             */
            'search' => ['nullable', 'string', 'max:255'],
            /**
             * Number of records per page.
             * @default 20
             * @example 30
             */
            'per_page' => ['nullable', 'integer', 'min:1', 'max:10000'],
        ]);
        $perPage = $request->input('per_page', PER_PAGE);
        $listCrops = $this->cropManager->list($request);

        return response()->json([
            "message" => "Success",
            "current_page" => 1,
            "total_page" => 1,
            "per_page" => (int)$perPage,
            "total" => count($listCrops),
            "data" => $listCrops,
        ]);
    }
}
