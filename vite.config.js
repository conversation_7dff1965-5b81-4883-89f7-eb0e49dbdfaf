import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import fg from 'fast-glob';
import path from 'path'

export default defineConfig({
    server: {
        port: 5173,
        cors: true,
    },
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                // Common
                'resources/build/js/AdminLTE.js',
                'resources/build/scss/adminlte.scss',
                'resources/build/js/common.js',
                'resources/build/scss/common.scss',
                // User
                'resources/build/js/pages/user/info.js',
                // custom
                'resources/build/js/custom/selectDataAjax.js',
                //Pages
                ...fg.sync('resources/build/js/pages/*.js'),
            ],
            refresh: true,
        }),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'resources'),
        },
    },
});
