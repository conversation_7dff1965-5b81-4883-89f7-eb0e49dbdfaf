import * as moment from "moment";
import locales from "../locales/locales";
import { DATE_TIME_FORMATS } from "../constants";

let language = $("body").data('locales'), trans = locales(language);

const {
    formatDate,
    formatMonth,
    formatTime,
    formatTimeClock,
    formatDateTime }
    = DATE_TIME_FORMATS[language];

function convertMomentFormatToDatepicker(format) {
    return format
        .replace(/YYYY/g, 'yyyy')
        .replace(/YY/g, 'yy')
        .replace(/MM/g, 'mm')
        .replace(/M/g, 'm')
        .replace(/DD/g, 'dd')
        .replace(/D/g, 'd');
}

// Using bootstrap-datepicker Plugin
export function datePicker(selector) {
    if ($(selector).length){
        $(selector).datepicker({
            format: convertMomentFormatToDatepicker(formatDate),
            language: language,
            todayHighlight : true,
            autoClose: true,
        }).on('changeDate', function(ev){
            $(this).datepicker('hide');
        });
    } else
        return false;
}

export function datePickerMultiple(selector,multidateNum = 3) {
    // giới hạn lại số năm
    let limitYearStart = moment().subtract(20, 'years').format(formatDate);
    let limitYearEnd = moment().add(20, 'years').format(formatDate);
    if ($(selector).length){
        $(selector).datepicker({
            icons:{
                previous: 'fas fa-chevron-left',
                next: 'fas fa-chevron-right',
            },
            useCurrent: false,
            format: convertMomentFormatToDatepicker(formatDate),
            language: language,
            todayHighlight : true,
            multidate: multidateNum,
            multidateSeparator: ", ",
            ignoreReadonly: true,
            startDate: limitYearStart,
            endDate: limitYearEnd,
            orientation: "bottom left",
        }).on('hide', function(e) {
            e.stopPropagation();
        });
    } else
        return false;
}

export function monthPicker(selector) {
    if ($(selector).length){
        $(selector).datepicker({
            viewMode: "months",
            minViewMode: "months",
            format: convertMomentFormatToDatepicker(formatMonth),
            weekStart: 1,
            language: language,
            todayHighlight : true,
            autoClose: true
        }).on('changeDate', function(ev){
            $(this).datepicker('hide');
        });
    } else
        return false
}

export function yearPicker(selector) {
    if ($(selector).length){
        $(selector).datepicker({
            viewMode: "years",
            minViewMode: "years",
            format: "yyyy",
            weekStart: 1,
            language: language,
            todayHighlight : true,
            autoClose: true
        }).on('changeDate', function(ev){
            $(this).datepicker('hide');
        });
    } else
        return false
}

export function dateRangePicker(selector) {
    const $containers = $(selector);
    if (!$containers.length) return false;

    $containers.each(function () {
      const $wrap  = $(this);
      const $start = $wrap.find('.start_at');
      const $end   = $wrap.find('.end_at');
      if (!$start.length || !$end.length) return; // skip if container has no start/end inputs

      const ops = {
        format: convertMomentFormatToDatepicker(formatDate),
        language: language,
        todayHighlight: true,
        autoclose: true, // correct option name for bootstrap-datepicker
      };

      // Re-init datepickers to avoid duplicate bindings if function is called multiple times
      $start.datepicker('destroy').datepicker(ops);
      $end.datepicker('destroy').datepicker(ops);

      // Set constraints based on current values (if any)
      const sVal = $start.val();
      const eVal = $end.val();
      if (sVal) $end.datepicker('setStartDate', sVal);
      if (eVal) $start.datepicker('setEndDate', eVal);

      // Bind events with namespace `.drp` so they can be easily unbound later
      $start.off('.drp')
        .on('changeDate.drp', function () {
          // When start changes, update min date for end
          $end.datepicker('setStartDate', $(this).val());
          $(this).datepicker('hide');
        })
        .on('clearDate.drp', function () {
          // If start is cleared, remove restriction from end
          $end.datepicker('setStartDate', null);
        });

      $end.off('.drp')
        .on('changeDate.drp', function () {
          // When end changes, update max date for start
          $start.datepicker('setEndDate', $(this).val());
          $(this).datepicker('hide');
        })
        .on('clearDate.drp', function () {
          // If end is cleared, remove restriction from start
          $start.datepicker('setEndDate', null);
        });
    });

    return true;
  }

export function monthRangePicker(selector){
    if ($(selector).length){
        let $start = $(selector).find('.start_at'),
            $end = $(selector).find('.end_at'),
            ops = {
                viewMode: "months",
                minViewMode: "months",
                format: convertMomentFormatToDatepicker(formatMonth),
                language: language,
                todayHighlight : true,
                autoClose: true
            }
        if ($start.length && $end.length) {
            $start.datepicker(ops).on('changeDate', function(ev){
                $end.datepicker('setStartDate', $(this).val())
                $(this).datepicker('hide');
            });
            $end.datepicker(ops).on('changeDate', function(ev){
                $start.datepicker('setEndDate', $(this).val())
                $(this).datepicker('hide');
            });
        }else
            return false;
    } else
        return false
}

// Using Jquery Clockpicker Plugin
export function timePicker(selector) {
    if ($(selector).length){
        $(selector).clockpicker({
            placement: 'bottom',
            format: convertMomentFormatToDatepicker(formatTimeClock),
            align: 'left',
            autoclose: true,
            'default': 'now'
        });
    } else
        return false;
}

export function dateTimeRangePicker(selector) {
    if ($(selector).length){
        let $start = $(selector).find('.start_at'),
            $end = $(selector).find('.end_at'),
            ops = {
                format: convertMomentFormatToDatepicker(formatDateTime),
                sideBySide:true,
                locale:moment.locale (language),
                icons:{
                    previous: 'fas fa-chevron-left',
                    next: 'fas fa-chevron-right',
                },
                showClose:true,
                minDate: false,
                maxDate:false,
                useCurrent: false,
            }
        if ($start.length && $end.length) {
            $start.datetimepicker(ops).on('dp.change', function(ev){
                if ($start.val()!=''){
                    $end.data("DateTimePicker").minDate($start.val());
                }else{
                    $end.data("DateTimePicker").minDate(false);
                }
            });
            $end.datetimepicker(ops).on('dp.change', function(ev){
                if ($end.val()!=''){
                    $start.data("DateTimePicker").maxDate($end.val());
                }else{
                    $start.data("DateTimePicker").maxDate(false);
                }
            });
            $start.datetimepicker(ops).on('dp.show', function(ev){
                if ($end.val()!=''){
                    $start.data("DateTimePicker").maxDate($end.val());
                }else{
                    $start.data("DateTimePicker").maxDate(false);
                }
                if ($start.val()!=''){
                    $end.data("DateTimePicker").minDate($start.val());
                }else{
                    $end.data("DateTimePicker").minDate(false);
                }
            });
            $end.datetimepicker(ops).on('dp.show', function(ev){
                if ($start.val()!=''){
                    $end.data("DateTimePicker").minDate($start.val());
                }else{
                    $end.data("DateTimePicker").minDate(false);
                }
                if ($end.val()!=''){
                    $start.data("DateTimePicker").maxDate($end.val());
                }else{
                    $start.data("DateTimePicker").maxDate(false);
                }
            });
        }else
            return false;
    } else
        return false
}

export function dateTimePicker(selector) {
    if ($(selector).length){
        $(selector).datetimepicker({
            format: convertMomentFormatToDatepicker(formatDateTime),
            sideBySide:true,
            locale:moment.locale (language),
            icons:{
                previous: 'fas fa-chevron-left',
                next: 'fas fa-chevron-right',
            },
            showClose:true,
            minDate: false,
            maxDate:false,
            useCurrent: false,
        });
    } else
        return false;
}

export function dateTimePickerMaxNow(selector) {
    if ($(selector).length){
        $(selector).datetimepicker({
            format: convertMomentFormatToDatepicker(formatDateTime),
            sideBySide:true,
            locale:moment.locale (language),
            icons:{
                previous: 'fas fa-chevron-left',
                next: 'fas fa-chevron-right',
            },
            showClose:true,
            minDate: false,
            maxDate:new Date(),
            useCurrent: false,
        });
    } else
        return false;
}
export function dateBeforeOrEqualToday(selector) {
    if ($(selector).length){
        $(selector).datepicker({
            format: convertMomentFormatToDatepicker(formatDate),
            language: language,
            todayHighlight: true,
            autoClose: true,
            minDate: false,
            maxDate: false,
            endDate: '0d',
            useCurrent: false,
        }).on('changeDate', function(ev){
            $(this).datepicker('hide');
        });
    } else
        return false;
}
export function monthPickerMaxNow(selector) {
    if ($(selector).length){
        $(selector).datepicker({
            viewMode: "months",
            minViewMode: "months",
            format: convertMomentFormatToDatepicker(formatMonth),
            weekStart: 1,
            language: language,
            todayHighlight : true,
            autoClose: true,
            endDate: "0m",
            ignoreReadonly: true,
        }).on('changeDate', function(ev){
            $(this).datepicker('hide');
        }).on('hide', function(e) {
            e.stopPropagation();
        });
    } else
        return false
}

export function datePickerNotPropagate(selector) {
    if ($(selector).length){
        $(selector).datepicker({
            format: convertMomentFormatToDatepicker(formatDate),
            language: language,
            todayHighlight : true,
            autoClose: true
        }).on('changeDate', function(ev){
            $(this).datepicker('hide');
        }).on('hide', function(e) {
            e.stopPropagation();
        });
    } else
        return false;
}

export function timeRangePicker(selector) {
    if ($(selector).length){
        let $start = $(selector).find('.start_at'),
            $end = $(selector).find('.end_at'),
            ops = {
                placement: 'bottom',
                format: convertMomentFormatToDatepicker(formatTime),
                align: 'left',
                autoclose: true,
                'default': 'now',
            };
        if ($start.length && $end.length) {
            $start.clockpicker(ops);
            $end.clockpicker(ops);
            $start.on('change', function (ev) {
                var endObject = $(this).closest('.rangeTime').find('.end_at');
                var endTime = endObject.val();
                var startTime = $(this).val();
                console.log(endTime);
                if (startTime != '' && endTime != '' && startTime > endTime){
                    let parent = $(this).closest('.timepicker');
                    console.log(endTime);
                    console.log(startTime);
                    var errorMessage = trans.before_or_equal.replace(":attribute", trans.start_at).replace(":date", endTime);
                    if (parent.find('.invalid-alert').length > 0) {
                        parent.find('.invalid-alert').text(errorMessage);
                    } else {
                        parent.append (`<label class="error-js invalid-alert text-danger">
                                    ` + errorMessage +`
                                    </label>`);
                    }
                    endObject.closest('.timepicker').find('.invalid-alert').remove();
                } else {
                    $(this).closest('.timepicker').find('.invalid-alert').remove();
                    endObject.closest('.timepicker').find('.invalid-alert').remove();
                }
            });
            $end.on('change', function (ev) {
                var startObject = $(this).closest('[data-picker="rangeTime"]').find('.start_at');
                var startTime = startObject.val();
                var endTime = $(this).val();
                if (endTime != '' && startTime != '' && startTime > endTime){
                    let parent = $(this).closest('.timepicker');
                    var errorMessage = trans.after_or_equal.replace(":attribute", trans.start_at).replace(":date", startTime);
                    if (parent.find('.invalid-alert').length > 0) {
                        parent.find('.invalid-alert').text(errorMessage);
                    } else {
                        parent.append (`<label class="error-js invalid-alert text-danger">
                                        ` + errorMessage +`
                                        </label>`);
                    }
                    startObject.closest('.timepicker').find('.invalid-alert').remove();
                } else {
                    startObject.closest('.timepicker').find('.invalid-alert').remove();
                    $(this).closest('.timepicker').find('.invalid-alert').remove();
                }
            });
        } else {
            return false;
        }
    } else
        return false
}
