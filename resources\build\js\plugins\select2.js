import locales from "../locales/locales"
let language = $('body').data('locales'),
    trans = locales(language);
export function select2Base(selector){
    if ($(selector).length){
        $(selector).select2({
            theme: 'bootstrap4',
            addCssClass : "error",
            language: language,
            width: 'auto',
        });
        $('.select2-search__field').css('width', '100%');
    }
}

export function select2NoSearch(selector){
    if ($(selector).length){
        $(selector).select2({
            theme: 'bootstrap4',
            addCssClass : "error",
            language: language,
            minimumResultsForSearch: -1,
            width: 'auto',
        });
        $('.select2-search__field').css('width', '100%');
    }
}
export function addPlaceHolder() {
    $("#member").select2({
        placeholder: trans.select_members,
    });
 }

export function select2Tag(selector, multiple = false){
    if ($(selector).length){
        $(selector).select2({
            theme: 'bootstrap4',
            addCssClass : "error",
            language: language,
            tags:true,
            multiple: multiple
        });
        $('.select2-search__field').css('width', '100%');
    }
}

export function select2KanbanTag(selector, multiple = true){
    if ($(selector).length){
        $(selector).select2({
            closeOnSelect: false,
            language: language,
            tags:true,
            multiple: multiple,
            tokenSeparators: [','],
        });
    }
}

export function changeIconArrowSelect2(selector = false){
    let icon = `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="7.4" viewBox="0 0 12 7.4">
    <path id="shape" d="M16.6,8.6,12,13.2,7.4,8.6,6,10l6,6,6-6Z" transform="translate(-6 -8.6)" fill="rgba(0,0,0,0.38)" fill-rule="evenodd"/>
  </svg>
  `;
    $('b[role="presentation"]').hide();
    // Single select arrows
    if (selector) {
        $(selector).closest(".form-group").find(`.select2-selection__arrow`).append(icon);
    } else {
        $(".select2-selection__arrow").append(icon);
    }

    // Multiple select arrows
    $('.select2-selection--multiple').each(function() {
        if (!$(this).find('.select2-selection__arrow').length) {
            $(this).css('position', 'relative').append(`<span class="select2-selection__arrow" style="position: absolute; top: 50%; right: 20px; transform: translateY(-50%); pointer-events: none; z-index: 1;">${icon}</span>`);
        }
    });
}
