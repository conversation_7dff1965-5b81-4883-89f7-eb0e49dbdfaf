<?php

namespace App\Helpers;

use App\Enums\FilterType;

class FilterHelper
{
    /**
     * Render filter badges HTML with flexible configuration
     * @param \Illuminate\Http\Request $request
     * @param array $config - Field configurations and formatters
     * @param array $options - Display options
     * @return string
     */
    public function renderFilterBadges($request, $config, $options = []): string
    {
        $defaultOptions = [
            'badge_class' => 'badge badge-primary badge-filter bgr',
            'wrapper_tag' => 'span',
            'separator' => '',
            'prefix' => '',
            'suffix' => ''
        ];

        $options = array_merge($defaultOptions, $options);

        $tagOpen = "<{$options['wrapper_tag']} class=\"{$options['badge_class']}\">";
        $tagClose = "</{$options['wrapper_tag']}>";
        $output = '';

        foreach ($config as $fieldConfig) {
            $field = $fieldConfig['field'];
            $value = '';

            if ($request->has($field) && $request->$field != null) {
                $value = $this->processField($request, $fieldConfig, $tagOpen, $tagClose);
                if ($value) {
                    $output .= $options['separator'] . $value;
                }
            }
        }

        return "<div class='filter-badges-wrapper'>" . $options['prefix'] . $output . $options['suffix'] . "</div>";
    }

    /**
     * Process single field with corresponding formatter
     * @param \Illuminate\Http\Request $request
     * @param array $fieldConfig
     * @param string $tagOpen
     * @param string $tagClose
     * @return string
     */
    private function processField($request, $fieldConfig, $tagOpen, $tagClose): string
    {
        $field = $fieldConfig['field'];
        $type = $fieldConfig['type'] ?? FilterType::TEXT->value;
        $formatter = $fieldConfig['formatter'] ?? null;
        $options = $fieldConfig['options'] ?? [];

        $rawValue = $request->$field;

        // Apply custom formatter if available
        if ($formatter && is_callable($formatter)) {
            return $formatter($rawValue, $tagOpen, $tagClose, $options);
        }

        // Process by type
        return $this->processFieldByType($rawValue, $type, $tagOpen, $tagClose, $options);
    }

    /**
     * Process field by type - using enum values
     * @param mixed $value
     * @param string $type
     * @param string $tagOpen
     * @param string $tagClose
     * @param array $options
     * @return string
     */
    private function processFieldByType($value, $type, $tagOpen, $tagClose, $options = []): string
    {
        switch ($type) {
            case FilterType::IDS->value:
                return $this->formatIds($value, $tagOpen, $tagClose, $options);

            case FilterType::ARRAY->value:
                return $this->formatArray($value, $tagOpen, $tagClose, $options);

            case FilterType::SELECT->value:
                return $this->formatSelect($value, $tagOpen, $tagClose, $options);

            case FilterType::BOOLEAN->value:
                return $this->formatBoolean($value, $tagOpen, $tagClose, $options);

            case FilterType::DATE->value:
                return $this->formatDate($value, $tagOpen, $tagClose, $options);

            case FilterType::RANGE->value:
                return $this->formatRange($value, $tagOpen, $tagClose, $options);

            case FilterType::TEXT->value:
                return $this->formatText($value, $tagOpen, $tagClose, $options);

            case FilterType::FIELD_FROM_DATABASE->value:
                return $this->formatFieldFromDatabase($value, $tagOpen, $tagClose, $options);

            default:
                return $this->formatText($value, $tagOpen, $tagClose, $options);
        }
    }

    /**
     * Format IDs (comma separated)
     */
    private function formatIds($value, $tagOpen, $tagClose, $options)
    {
        $prefix = $options['prefix'] ?? '#';
        $separator = $options['separator'] ?? ',';

        $ids = array_filter(array_map('trim', explode($separator, StringHelper::escapeHtml($value))));
        $output = '';

        foreach ($ids as $id) {
            $output .= $tagOpen . $prefix . $id . $tagClose;
        }

        return $output;
    }

    /**
     * Format array values - display multiple options in single badge separated by |
     */
    private function formatArray($value, $tagOpen, $tagClose, $options)
    {
        if (!is_array($value)) {
            return '';
        }

        $mapping = $options['mapping'] ?? [];
        $separator = $options['separator'] ?? ' | ';

        $displayValues = [];
        foreach ($value as $item) {
            $displayValue = isset($mapping[$item]) ? $mapping[$item] : $item;
            $displayValues[] = StringHelper::escapeHtml($displayValue);
        }

        $combinedValue = implode($separator, $displayValues);
        return $tagOpen . $combinedValue . $tagClose;
    }

    /**
     * Format select/enum values
     */
    private function formatSelect($value, $tagOpen, $tagClose, $options)
    {
        $mapping = $options['mapping'] ?? [];
        $displayValue = isset($mapping[$value]) ? $mapping[$value] : $value;

        return $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
    }

    /**
     * Format boolean values
     */
    private function formatBoolean($value, $tagOpen, $tagClose, $options)
    {
        $labels = $options['labels'] ?? ['No', 'Yes'];
        $displayValue = $value ? $labels[1] : $labels[0];

        return $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
    }

    /**
     * Format date values
     */
    private function formatDate($value, $tagOpen, $tagClose, $options)
    {
        // Format date values with optional format from $options, default to 'd/m/Y'
        $format = $options['format'] ?? 'd/m/Y';

        $displayValue = \App\Helpers\DateTimeHelper::formatLanguage($value, 'date');

        return $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
    }

    /**
     * Format range values (from-to)
     */
    private function formatRange($value, $tagOpen, $tagClose, $options)
    {
        $separator = $options['separator'] ?? ' - ';
        $prefix = $options['prefix'] ?? '';
        $suffix = $options['suffix'] ?? '';

        if (is_array($value) && count($value) == 2) {
            $displayValue = $prefix . $value[0] . $separator . $value[1] . $suffix;
        } else {
            $displayValue = $prefix . $value . $suffix;
        }

        return $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
    }

    /**
     * Format text values
     */
    private function formatText($value, $tagOpen, $tagClose, $options)
    {
        $prefix = $options['prefix'] ?? '';
        $suffix = $options['suffix'] ?? '';
        $maxLength = $options['max_length'] ?? null;

        $displayValue = $prefix . $value . $suffix;

        if ($maxLength && strlen($displayValue) > $maxLength) {
            $displayValue = substr($displayValue, 0, $maxLength) . '...';
        }

        return $tagOpen . StringHelper::escapeHtml($displayValue) . $tagClose;
    }
    /**
     * Format find field from database
     */
    private function formatFieldFromDatabase($value, $tagOpen, $tagClose, $options)
    {
        $table = $options['table'] ?? '';
        $field = $options['field'] ?? '';

        // Check if table class is valid
        if (empty($table) || !class_exists($table)) {
            return "";
        }

        // Convert single value to array for consistent handling
        $valueArray = is_array($value) ? $value : [$value];

        // Query records and format as array
        $records = $table::select($field)->whereIn('id', $valueArray)->get();

        if ($records->isEmpty()) {
            return '';
        }

        $values = $records->pluck($field)->toArray();

        // Use formatArray to handle the display
        return $this->formatArray($values, $tagOpen, $tagClose, $options);
    }
}
