<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CropResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return
            [
                /**
                 * Crop ID.
                 * @var int
                 * @example 1
                 */
                "id" => $this->id,
                /**
                 * Crop name.
                 * @var string
                 * @example "Rau muống"
                 */
                "name" => $this->name,
                /**
                 * Crop cost.
                 * @var array
                 * @example {"gold": 10, "gem": 2}
                 */
                "cost" => $this->cost,
                /**
                 * Growth phases as array of objects.
                 * @var array
                 * @example {"first": [{"name": "Gieo hạt", "duration": 14}, {"name": "Phát triển", "duration": 14}, {"name": "Thu hoạch", "duration": 7}], "next": {"phases": [{"name": "Phát triển", "duration": 15}, {"name": "Thu hoạch", "duration": 15}], "count": 2}}
                 */
                'growth_phases' => $this->growth_phases,
                /**
                 * Growth phase count.
                 * @var int
                 * @example 6
                 */
                "growth_phase_count" => $this->growth_phases->count(),
                /**
                 * Harvest phases as array of objects.
                 * @var array
                 * @example [{"name": "Lần 1", "duration": 35}, {"name": "Lần 2", "duration": 30}]
                 */
                'harvest_phases' => $this->harvest_phases,
                /**
                 * Harvest phase count.
                 * @var int
                 * @example 1
                 */
                "harvest_phase_count" => $this->harvest_phases->count(),
                /**
                 * Crop description.
                 * @var string
                 * @example "Cây dễ trồng, thích hợp vùng nhiệt đới."
                 */
                "description" => $this->description,
                /**
                 * Crop group ID.
                 * @var int
                 * @example 1
                 */
                "crop_group_id" => $this->crop_group_id,
                /**
                 * Crop group name.
                 * @var string
                 * @example "Rau ăn lá"
                 */
                "crop_group_name" => $this->crop_group->name,
                /**
                 * Crop bundle key.
                 * @var string
                 * @example "rau_muong"
                 */
                "bundle_key" => $this->bundle_key,
            ];
    }
}
