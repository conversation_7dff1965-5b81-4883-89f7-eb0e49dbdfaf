<?php

namespace App\Http\Controllers;

use App\Helpers\FilterHelper;
use App\Enums\FilterType;
use App\Http\Requests\CropRequest;
use App\Logics\CropManager;
use App\Logics\CropGroupManager;
use App\Helpers\StringHelper;
use App\Models\Crop;
use App\Models\Language;
use Barryvdh\Debugbar\Facades\Debugbar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CropController extends Controller
{
    private $cropManager;
    private $cropGroupManager;
    private $filterHelper;

    public function __construct(
        CropManager $cropManager,
        CropGroupManager $cropGroupManager,
        FilterHelper $filterHelper
    ) {
        $this->cropManager = $cropManager;
        $this->cropGroupManager = $cropGroupManager;
        $this->filterHelper = $filterHelper;
    }

    /**
     * Display a listing of crops.
     */
    public function index(Request $request)
    {
        $languageId = auth()->user()->language_id;

        // Get filters from request
        $filters = [
            'keyword' => $request->input('keyword'),
            'crop_group_id' => $request->input('crop_group_id'),
        ];

        // Get crops list with filters and multilingual support
        $crops = $this->cropManager->getCropsList($languageId, $filters);

        // Pagination
        $perPage = $request->has('per_page') ? $request->input('per_page') : PER_PAGE;
        $crops = $crops->paginate($perPage);

        if ($redirect = $this->redirectInvalidPage($crops, $request)) {
            return $redirect;
        }

        // Get crop groups for filter dropdown
        $cropGroups = $this->cropGroupManager->getCropGroupsList($languageId)->get();
        $cropGroupOptions = [];
        foreach ($cropGroups as $group) {
            $name = $group->locales->first() ? $group->locales->first()->name : __('language.na');
            $cropGroupOptions[$group->id] = $name;
        }

        // Filter configuration for FilterHelper
        $filterConfig = [
            FilterType::createConfig('keyword', FilterType::TEXT),
            FilterType::createConfig('crop_group_id', FilterType::SELECT, [
                'mapping' => $cropGroupOptions
            ])
        ];

        // Get filter data for display
        $isFilter = $this->filterHelper->renderFilterBadges($request, $filterConfig);

        return view('crops.index', [
            'crops' => $crops,
            'isFilter' => $isFilter,
            'filterConfig' => $filterConfig,
            'cropGroupOptions' => $cropGroupOptions
        ]);
    }

    /**
     * Show the form for creating a new crop.
     */
    public function create()
    {
        $formData = $this->prepareFormData();

        return view('crops.form', $formData);
    }

    /**
     * Store a newly created crop in storage.
     */
    public function store(CropRequest $request)
    {
        try {
            DB::beginTransaction();

            // Generate bundle_key từ vi.name sử dụng StringHelper
            $stringHelper = new StringHelper();
            $viName = $request->input('vi.name');
            $bundleKey = $viName ? $stringHelper->create_slug($viName, '_') : '';

            // Prepare crop data
            $cropData = [
                'crop_group_id' => $request->input('crop_group_id'),
                'bundle_key' => $bundleKey,
                'cost' => $this->prepareCostData($request),
                'harvest_type' => $request->input('harvest_type'),
                'harvest_phases' => $request->input('harvest_phases'),
                'growth_phases' => $request->input('growth_phases'),
            ];


            Log::info('cropData', $cropData);
            Log::info('Crop store request',$request->all());

            // dd($cropData);
            // Create the crop record
            $crop = Crop::create($cropData);

            // Save multilingual data
            $this->saveLocaleData($crop, $request);

            DB::commit();

            // Return JSON for AJAX requests
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => __('language.crop_created_successfully'),
                    'redirect_url' => route('crops.index')
                ]);
            }

            // Traditional redirect
            return redirect()->route('crops.index')
                ->with('success', __('language.crop_created_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();

            // Return JSON error for AJAX requests
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => __('language.crop_create_failed'),
                    'error' => $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->withInput()
                ->with('error', __('language.crop_create_failed'));
        }
    }

    /**
     * Prepare cost data from request
     *
     * @param $request
     * @return array
     */
    private function prepareCostData($request): array
    {
        $costData = ['gold' => $request->input('cost.gold')];

        if ($request->filled('cost.gem')) {
            $costData['gem'] = $request->input('cost.gem');
        }

        return $costData;
    }

    /**
     * Show the form for editing the specified crop.
     */
    public function edit($id)
    {
        // Proper eager loading with all necessary relationships
        $crop = Crop::with(['locales', 'cropGroup.locales'])->findOrFail($id);
        $formData = $this->prepareFormData($crop);

        return view('crops.form', $formData);
    }

    /**
     * Update the specified crop in storage.
     */
    public function update($id, CropRequest $request)
    {
        try {
            DB::beginTransaction();

            // Load crop with relationships for proper data handling
            $crop = Crop::with(['locales'])->findOrFail($id);

            // Update the crop record
            $crop->update([
                'crop_group_id' => $request->input('crop_group_id'),
                'cost' => $this->prepareCostData($request),
                'harvest_type' => $request->input('harvest_type'),
                'harvest_phases' => $request->input('harvest_phases'),
                'growth_phases' => $request->input('growth_phases'),
            ]);

            // Save multilingual data
            $this->saveLocaleData($crop, $request);

            DB::commit();

            // Return JSON for AJAX requests
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => __('language.crop_updated_successfully'),
                    'redirect_url' => route('crops.index')
                ]);
            }

            // Traditional redirect
            return redirect()->route('crops.index')
                ->with('success', __('language.crop_updated_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();

            // Return JSON error for AJAX requests
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => __('language.crop_update_failed'),
                    'error' => $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->withInput()
                ->with('error', __('language.crop_update_failed'));
        }
    }

    /**
     * Remove the specified crop from storage.
     */
    public function destroy($id)
    {
        try {
            $crop = Crop::findOrFail($id);

            // Soft delete the crop
            $crop->delete();

            return response()->json([
                'status' => 200,
                'msg' => [
                    'title' => __('language.success'),
                    'text' => __('language.crop_delete_success')
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'msg' => [
                    'title' => __('language.failure'),
                    'text' => __('language.crop_delete_failed')
                ]
            ]);
        }
    }

    /**
     * Prepare form data for create/edit views.
     */
    private function prepareFormData($crop = null)
    {
        $isEdit = !is_null($crop);
        $languageId = auth()->user()->language_id;

        // Get all active languages for multilingual support
        $availableLanguages = Language::active()->get();

        // Get crop groups for dropdown with proper eager loading
        $cropGroups = $this->cropGroupManager->getCropGroupsList($languageId)->get();
        $cropGroupOptions = [];
        foreach ($cropGroups as $group) {
            $name = $group->locales->first() ? $group->locales->first()->name : __('language.na');
            $cropGroupOptions[$group->id] = $name;
        }

        // Add constants for language and crop types
        $const = [
            'vi' => Language::VI,
            'single' => Crop::SINGLE,
            'multiple' => Crop::MULTIPLE,
            'max_phase_duration_first' => Crop::MAX_PHASE_DURATION_FIRST ?? 14,
            'max_phase_duration_next' => Crop::MAX_PHASE_DURATION_NEXT ?? 30,
            'max_phase_count_next' => Crop::MAX_PHASE_COUNT_NEXT ?? 3,
        ];

        // Prepare crop data for JS (edit mode)
        $cropDataForJs = null;
        if ($isEdit && $crop) {
            $cropDataForJs = [
                'id' => $crop->id,
                'crop_group_id' => $crop->crop_group_id,
                'harvest_type' => $crop->harvest_type,
                'cost' => $crop->cost,
                'harvest_phases' => $crop->harvest_phases,
                'growth_phases' => $crop->growth_phases,
                'locales' => $crop->locales->toArray()
            ];
        }

        $formData = [
            'isEdit' => $isEdit,
            'crop' => $crop,
            'cropDataForJs' => $cropDataForJs, // Data đã format cho JS
            'const' => $const,
            'availableLanguages' => $availableLanguages,
            'cropGroupOptions' => $cropGroupOptions,
            'route' => $isEdit ? route('crops.update', $crop->id) : route('crops.store'),
            'headerTitle' => $isEdit ? __('language.crop_title_update') : __('language.crop_title_create'),
        ];

        // Add debug info to help with troubleshooting data loading
        if ($isEdit && $crop) {
            Debugbar::info([
                'form_data' => $formData, // Log toàn bộ $formData
                'crop_data' => [
                    'id' => $crop->id,
                    'locales_count' => $crop->locales->count(),
                    'vietnamese_name' => $crop->getNameByLanguage(Language::VI),
                    'vietnamese_description' => $crop->getDescriptionByLanguage(Language::VI),
                    'cost' => $crop->cost, // Log cả cost (gold, gem)
                    'harvest_phases' => $crop->harvest_phases,
                    'growth_phases' => $crop->growth_phases,
                ],
                'locales' => $crop->locales->toArray(), // Log tất cả locales
                'vietnamese_locale' => $crop->getLocaleByCode(Language::VI) ? $crop->getLocaleByCode(Language::VI)->toArray() : 'Not found',
            ]);
        }

        return $formData;
    }

    /**
     * Save multilingual locale data for the crop.
     */
    private function saveLocaleData($crop, $request)
    {
        $languages = Language::active()->get()->keyBy('code');

        foreach ($languages as $code => $language) {
            $langData = $request->input($code);

            if ($langData && !empty($langData['name'])) {
                $crop->locales()->updateOrCreate(
                    ['language_id' => $language->id],
                    [
                        'name' => $langData['name'],
                        'description' => $langData['description'] ?? ''
                    ]
                );
            }
        }
    }
}
