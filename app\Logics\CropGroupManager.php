<?php

namespace App\Logics;

use App\Helpers\StringHelper;
use App\Models\CropGroup;

class CropGroupManager
{
    /**
     * List crop groups
     * @param array $data Validated data from request
     * @param int $languageId Language ID for localization
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function list(array $data, int $languageId)
    {
        $query = CropGroup::withLocale($languageId)->active();

        // Filter by parent_id
        if (isset($data['parent_id'])) {
            $query->where('parent_id', $data['parent_id']);
        }

        // Search by name in locales
        if (!empty($data['search'])) {
            $stringHelper = new StringHelper();
            $search = $stringHelper->formatStringWhereLike($data['search']);
            $query->whereHas('locales', function($q) use ($search, $languageId) {
                $q->forLanguage($languageId)
                  ->where('name', 'like', "%{$search}%");
            });
        }

        return $query->orderBy('id', 'asc');
    }

    /**
     * Get crop groups list with multilingual support
     * @param int $languageId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getCropGroupsList($languageId)
    {
        return CropGroup::withLocale($languageId)->active()->orderBy('id', 'asc');
    }
}
