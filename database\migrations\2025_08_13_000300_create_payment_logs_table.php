<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('payment_logs', function (Blueprint $table) {
            $table->id();
            $table->uuid('payment_id');
            $table->string('level')->default('info'); // info|warning|error
            $table->string('message');
            $table->json('context')->nullable();
            $table->timestamps();

            $table->index('payment_id');
        });
    }

    public function down(): void {
        Schema::dropIfExists('payment_logs');
    }
};
