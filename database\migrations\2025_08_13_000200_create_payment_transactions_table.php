<?php

use App\Enums\TransactionStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->uuid('payment_id');
            $table->string('event'); // requested|return|ipn|query|expire
            $table->string('status'); // TransactionStatus
            $table->string('provider_code')->nullable(); // raw provider code (e.g., vnp_TransactionStatus)
            $table->string('provider_message')->nullable();
            $table->boolean('signature_valid')->default(false);
            $table->json('request_payload')->nullable();
            $table->json('response_payload')->nullable();
            $table->timestamps();

            $table->index(['payment_id']);
            $table->index(['payment_id','event']);
        });
    }

    public function down(): void {
        Schema::dropIfExists('payment_transactions');
    }
};
