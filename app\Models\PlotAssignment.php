<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PlotAssignment extends Model
{
    protected $fillable = ['plot_id', 'user_id', 'start_at', 'end_at'];

    protected $casts = ['start_at' => 'date', 'end_at' => 'date'];

    public function plot()
    {
        return $this->belongsTo(Plot::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function admin()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }
}
