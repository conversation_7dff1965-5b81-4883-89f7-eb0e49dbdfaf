<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Logics\PlotManager;
use Dedoc\Scramble\Attributes\Parameter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PlotController extends Controller
{
    public function __construct(protected PlotManager $plotManager) {}

    /**
     * List plots
     *
     * List plots of user
     * 
     * @response App\Http\Resources\ListPlotResource[]
     * @paginated
     */
    #[Parameter('query', 'page', 'Page of pagination', type: 'int', example: 1)]
    #[Parameter('query', 'per_page', 'Per page of pagination', type: 'int', example: PER_PAGE)]
    public function list(Request $request): JsonResponse
    {
        $payload = $request->all(); //TODO: use validated()
        $result = $this->plotManager->getPlots(Auth::user(), $payload);

        return $this->apiJsonPagination($result);
    }
}
