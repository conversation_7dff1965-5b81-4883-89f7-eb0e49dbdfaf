import Layout from "./custom/Layout";
import Form from "./custom/Form";
import {checkPasswordConfirmValid} from "./helper/FormValid";

import {
    datePicker,
    dateRangePicker,
    timePicker,
    monthPicker,
    monthRangePicker,
    dateTimeRangePicker,
    dateTimePicker,
    dateTimePickerMaxNow, monthPickerMaxNow, yearPicker, dateBeforeOrEqualToday,timeRangePicker
} from "./plugins/datetimepicker";
import {changeIconArrowSelect2, select2Base, select2NoSearch, select2Tag} from "./plugins/select2";
import {fancybox2} from "./plugins/fancybox2";
import {summernote} from "./plugins/summernote";

import ModalChangePassword from "./custom/modalChangePassword";
import dynamicSelectOption from "./custom/dynamicSelectOption";
import deleteRowTable from "./custom/deleteRowTable";

import {selectUsers, selectTasks, selectUserManager, selectUserTaskOfDay, selectSprints} from "./custom/selectDataAjax";
import addButtonRemoveValueSelect2 from "./custom/addButtonRemoveValueSelect2";

import NavTreeviewAddon from "./custom/navTreeviewAddon";
import changeCSS from "./custom/changeCss";
import { addPlaceHolder } from "./plugins/select2";

import firebaseConfig from "./custom/fireBase";

$(function (){
    Layout();
    Form();
    NavTreeviewAddon();

    //tooltip
    $('[data-toggle="tooltip"]').tooltip({
        trigger:'hover',
        container: '.content-wrapper'
    })

    // Modal change Password
    ModalChangePassword();

    // datetimepicker plugin
    datePicker('[data-picker="date"]');
    dateRangePicker('[data-picker="rangeDate"]');
    timeRangePicker('[data-picker="rangeTime"]');
    monthPicker('[data-picker="month"]');
    yearPicker('[data-picker="year"]');
    monthRangePicker('[data-picker="rangeMonth"]');
    timePicker('[data-picker="clock"]');
    dateTimeRangePicker('[data-picker="date-time"]');
    dateTimePicker('[data-picker="datetime"]')
    dateTimePickerMaxNow('[data-picker="datetimeMaxNow"]');
    monthPickerMaxNow('[data-picker="month-max-now"]');
    dateBeforeOrEqualToday('[data-picker="dateBeforeOrEqualToday"]');

    // select2 plugin
    select2Base('.select2-base');
    select2NoSearch('.select2-noSearch');
    select2Tag('.select2-tag');
    select2Tag('.select2-multi-tag', true);

    // fancybox2 plugin
    fancybox2('.fancybox2');

    // summernote plugin
    summernote('.summernote');

    // Select Data Ajax
    selectUsers('.select2-data-users');
    selectUserManager('.select2-data-user-manager');
    selectUserManager('.select2-data-user-not-delete');
    selectTasks('.select2-data-tasks');
    selectSprints('.select2-data-sprints');
    selectUserTaskOfDay('.select2-data-user-task-of-day');

    // add button remove value select2 if select2 not require
    addButtonRemoveValueSelect2()
    
    // change icon arrow select2
    changeIconArrowSelect2();
    //Events
    $('form #confirm_password[required]').on('invalid change keyup', checkPasswordConfirmValid);
    $(document).on('click', '.delete-row-table', deleteRowTable);
    $(document).on('change', '.dynamic-select-option', dynamicSelectOption);

    $(document).on('submit','form',function(){
        $('button[type="submit"]').prop('disabled', true);
    })

    $(document).on('select2:open', () => {
        if(document.querySelector(".select2-container--open .select2-search__field")){
            document.querySelector(".select2-container--open .select2-search__field").focus();
        }else{
            let allFound = document.querySelectorAll('.select2-search__field');
            allFound[allFound.length - 1].focus();
        }

    });
    changeCSS();
    $(document).on('click', '#butonAddMember', addPlaceHolder);

    let notification = ("Notification" in window) ? Notification.permission : '';
    if(window.location.protocol == 'https:' && notification == 'granted'){
        firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        receiveMessage(messaging);

        // if page previous is login then call ajax save token firebase
        if(document.referrer == window.location.protocol + "//" + window.location.host + "/login"){
            saveTokenFireBase(messaging);
        }

        $(document).on('click', '#logout', function (e){
            event.preventDefault();
            messaging
                .requestPermission()
                .then(function () {
                    return messaging.getToken()
                })
                .then(function (response) {
                    $.ajaxSetup({
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        }
                    });
                    $.ajax({
                        url: '/device',
                        type: 'DELETE',
                        data: JSON.stringify({device_token: response}),
                        dataType: 'JSON',
                        success: function (response) {
                            document.getElementById('logout-form').submit();
                        },
                        error: function (error) {
                            alert(error);
                        },
                    });

                }).catch(function (error) {
                    alert(error);
                });
        });
    }else{
        $(document).on('click', '#logout', function (e){
            event.preventDefault();
            document.getElementById('logout-form').submit();
        });
    }

    if ('permissions' in navigator) {
        navigator.permissions.query({name: 'notifications'}).then(function (notificationPerm) {
            notificationPerm.onchange = function () {
                if (window.location.protocol == 'https:' && notificationPerm.state == 'granted'){
                    if (!firebase.apps.length) {
                       firebase.initializeApp(firebaseConfig);
                    }
                    const messaging = firebase.messaging();

                    receiveMessage(messaging);

                    saveTokenFireBase(messaging);
                }
            };
        });
    }

    function saveTokenFireBase(messaging){
        messaging
            .requestPermission()
            .then(function () {
                return messaging.getToken()
            })
            .then(function (response) {
                console.log(response);
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
                $.ajax({
                    url: '/device',
                    type: 'POST',
                    data: JSON.stringify({
                        device_token: response,
                        device_information: 'web'
                    }),
                    dataType: 'JSON',
                    success: function (response) {
                    },
                    error: function (error) {
                        alert(error);
                    },
                });

            }).catch(function (error) {
                alert(error);
            });
    }

    function receiveMessage(messaging){
        messaging.onMessage(function (payload) {
            if (!document.hidden) {
                const title = payload.notification.title;
                const options = {
                    body: payload.notification.body,
                    icon: payload.notification.icon,
                    click_action: payload.notification.click_action,
                    data: { click_action:payload.notification.click_action },
                };
                var notification = new Notification(title, options);
                notification.onclick = function(event) {
                    event.preventDefault(); //prevent the browser from focusing the Notification's tab
                    window.open(payload.notification.click_action, '_blank');
                    notification.close();
                }
            }
        });
    }
    jQuery.validator.setDefaults({
        // This will ignore all hidden elements alongside `.summernote` elements
        // that have no `name` attribute
        ignore: ":hidden:not(.summernote),.note-editable"
    });
})
