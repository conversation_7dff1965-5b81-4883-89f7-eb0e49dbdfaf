<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Crop;
use App\Models\CropLocale;

class CropSeeder extends Seeder
{
    public function run(): void
    {
        $crops = [
            [
                'crop_group_id' => 1, // Rau ăn lá
                'cost' => ['gold' => 10, 'gem' => 1],
                'harvest_phases' => [
                    ['name' => 'Lần 1', 'duration' => 30],
                    ['name' => 'Lần 2', 'duration' => 30]
                ],
                'growth_phases' => [
                    'first' => [
                        ['name' => 'Gieo hạt', 'duration' => 10],
                        ['name' => 'Phát triển', 'duration' => 12],
                        ['name' => 'Ra lá', 'duration' => 8]
                    ],
                    'next' => [
                        'phases' => [
                            ['name' => 'Tái phát triển', 'duration' => 15],
                            ['name' => 'Chuẩn bị thu hoạch', 'duration' => 15]
                        ],
                        'count' => 2
                    ]
                ],
                'harvest_type' => Crop::MULTIPLE,
                'bundle_key' => 'rau_muong',
                'locales' => [
                    [
                        'language_id' => 1, // Vietnamese
                        'name' => 'Rau muống',
                        'description' => "Rau muống là loại rau ăn lá phổ biến tại Việt Nam.\nDễ trồng, phát triển nhanh, thích hợp với khí hậu nhiệt đới.\nCó thể thu hoạch nhiều lần trong cùng một mùa vụ.\nThường dùng trong các món xào, luộc hoặc ăn kèm lẩu."
                    ]
                ]
            ],
            [
                'crop_group_id' => 2, // Cây ăn quả
                'cost' => ['gold' => 15, 'gem' => 2],
                'harvest_phases' => [
                    ['name' => 'Lần 1', 'duration' => 60]
                ],
                'growth_phases' => [
                    'first' => [
                        ['name' => 'Gieo hạt', 'duration' => 7],
                        ['name' => 'Phát triển thân', 'duration' => 14],
                        ['name' => 'Ra quả', 'duration' => 14],
                        ['name' => 'Chín', 'duration' => 10]
                    ]
                ],
                'harvest_type' => Crop::SINGLE,
                'bundle_key' => 'ca_chua',
                'locales' => [
                    [
                        'language_id' => 1, // Vietnamese
                        'name' => 'Cà chua',
                        'description' => "Cà chua là cây trồng ăn quả giàu giá trị dinh dưỡng.\nChứa nhiều vitamin C, A, kali và chất chống oxy hóa lycopene.\nThích hợp trồng ở vùng khí hậu ôn hòa.\nThường dùng trong salad, nước sốt hoặc ăn tươi."
                    ]
                ]
            ],
            [
                'crop_group_id' => 1, // Rau ăn lá
                'cost' => ['gold' => 22, 'gem' => 3],
                'harvest_phases' => [
                    ['name' => 'Thu hoạch', 'duration' => 40]
                ],
                'growth_phases' => [
                    'first' => [
                        ['name' => 'Gieo hạt', 'duration' => 6],
                        ['name' => 'Ra lá', 'duration' => 14],
                        ['name' => 'Cuốn bắp', 'duration' => 12]
                    ]
                ],
                'harvest_type' => Crop::SINGLE,
                'bundle_key' => 'bap_cai',
                'locales' => [
                    [
                        'language_id' => 1, // Vietnamese
                        'name' => 'Bắp cải',
                        'description' => "Bắp cải là loại rau ăn lá giàu dinh dưỡng.\nChứa nhiều vitamin C, K, chất xơ và các hợp chất chống oxy hóa.\nPhù hợp trồng ở vùng khí hậu mát mẻ.\nThường dùng trong món luộc, xào hoặc làm gỏi."
                    ]
                ]
            ],
            [
                'crop_group_id' => 3, // Họ bầu bí
                'cost' => ['gold' => 25, 'gem' => 4],
                'harvest_phases' => [ 
                    ['name' => 'Thu hoạch', 'duration' => 50]
                ],
                'growth_phases' => [
                    'first' => [
                        ['name' => 'Gieo hạt', 'duration' => 5],
                        ['name' => 'Phát triển dây', 'duration' => 14],
                        ['name' => 'Ra quả', 'duration' => 14],
                        ['name' => 'Chín', 'duration' => 12]
                    ]
                ],
                'harvest_type' => Crop::SINGLE,
                'bundle_key' => 'dua_hau',
                'locales' => [
                    [
                        'language_id' => 1, // Vietnamese
                        'name' => 'Dưa hấu',
                        'description' => "Dưa hấu là loại cây ăn quả ngọt, mọng nước.\nThuộc họ bầu bí, thường được trồng vào mùa hè.\nPhù hợp để ăn tươi, làm nước ép hoặc sinh tố.\nGiàu vitamin A và C, tốt cho sức khỏe."
                    ]
                ]
            ],
            [
                'crop_group_id' => 3, // Họ bầu bí
                'cost' => ['gold' => 12, 'gem' => 1],
                'harvest_phases' => [ 
                    ['name' => 'Thu hoạch', 'duration' => 35]
                ],
                'growth_phases' => [
                    'first' => [
                        ['name' => 'Gieo hạt', 'duration' => 4],
                        ['name' => 'Phát triển dây', 'duration' => 14],
                        ['name' => 'Ra quả', 'duration' => 14]
                    ]
                ],
                'harvest_type' => Crop::SINGLE,
                'bundle_key' => 'bi_do',
                'locales' => [
                    [
                        'language_id' => 1, // Vietnamese
                        'name' => 'Bí đỏ',
                        'description' => "Bí đỏ thuộc họ bầu bí, có giá trị dinh dưỡng cao.\nGiàu vitamin A, tốt cho thị lực và hệ miễn dịch.\nThường dùng trong món súp, hấp hoặc nướng.\nPhù hợp trồng ở vùng khí hậu ấm áp."
                    ]
                ]
            ]
        ];

        foreach ($crops as $cropData) {
            $crop = Crop::firstOrCreate(
                ['bundle_key' => $cropData['bundle_key']],
                [
                    'crop_group_id' => $cropData['crop_group_id'],
                    'cost' => $cropData['cost'],
                    'harvest_phases' => $cropData['harvest_phases'],
                    'growth_phases' => $cropData['growth_phases'],
                    'harvest_type' => $cropData['harvest_type'],
                ]
            );

            foreach ($cropData['locales'] as $locale) {
                CropLocale::updateOrCreate(
                    [
                        'crop_id' => $crop->id,
                        'language_id' => $locale['language_id'],
                    ],
                    [
                        'name' => $locale['name'],
                        'description' => $locale['description']
                    ]
                );
            }
        }
    }
}