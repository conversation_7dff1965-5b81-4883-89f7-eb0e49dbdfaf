<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Plot\PlotAssignRequest;
use App\Models\Land;
use App\Models\Plot;
use App\Models\PlotAssignment;

class LandController extends Controller
{
    /**
     * Display the grid of plots for a specific land.
     */
    public function grid(Land $land)
    {
        $plots = Plot::with([
            'currentAssignment.user:id,name,email,phone',
            'currentAssignment:id,user_id,plot_id,start_at,end_at'
        ])
            ->select(['id', 'x', 'y'])
            ->where('land_id', $land->id)
            ->orderBy('y')->orderBy('x')
            ->get()
            ->withAppends(['status']);
        $users = \App\Models\User::select('id', 'name', 'email')
            ->orderBy('id', 'desc')->limit(100)->get();

        return view('lands.grid', compact('land', 'plots', 'users'));
    }

    /**
     * Bulk assign multiple plots to a user.
     */
    public function assignPlots(PlotAssignRequest $request)
    {
        $data = $request->validated();
        $plots = Plot::with('currentAssignment:id,plot_id')
            ->select(['id'])
            ->whereIn('id', $data['plot_ids'])
            ->get()
            ->keyBy('id');

        $dataInsert = [];
        $ok = 0;
        $skipped = 0;
        $now = now();
        foreach ($data['plot_ids'] as $pid) {
            if (!data_get($plots, $pid) || data_get($plots, "$pid.currentAssignment")) {
                $skipped++;
                continue;
            }
            $dataInsert[] = [
                'plot_id'  => $pid,
                'user_id'  => $data['user_id'],
                'start_at' => data_get($data, 'start_at'),
                'end_at'   => data_get($data, 'end_at'),
                'created_at' => $now,
                'updated_at' => $now,
            ];
            $ok++;
        }
        PlotAssignment::insert($dataInsert);
        $message = __('language.count_asssigned', ['count' => $ok]) . ($skipped ? __('language.count_skipped', ['count' => $skipped]) : '');
        return $this->apiJson(message: $message);
    }
}
