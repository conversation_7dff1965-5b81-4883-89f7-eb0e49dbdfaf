import vi from "./vi";
import en from "./en";
import ja from "./ja";

const languages = { vi, en, ja };

function locales(locale = "vi") {
    return languages[locale];
}

export function trans(textKey, attributes) {
    const language = document.body.dataset.locales;
    const context = locales(language);
    const text = getByPath(context, textKey);
    if (!attributes) {
        return text;
    }
    return text.replace(/:([a-zA-Z0-9_]+):/g, (match, key) => {
        return attributes.hasOwnProperty(key) ? attributes[key] : match;
    });
}

function getByPath(obj, path) {
    return path.split('.').reduce((acc, key) => {
        if (acc && typeof acc === 'object' && key in acc) {
            return acc[key];
        }
        return undefined;
    }, obj) ?? path;
}

export default locales;