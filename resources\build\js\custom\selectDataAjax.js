import { escapeHtml } from "./formatString";
let language = $("body").data('locales');

// Cache default avatar URL to avoid recreating it every time
const DEFAULT_AVATAR_URL = escapeHtml(window.location.origin + "/images/user-default.png");
export function selectUsers(selector){
    let select = $(selector);

    if (select.length){
        let formatRepo = (repo) => {
            if (repo.loading) {
                return repo.text;
            }
            let name = escapeHtml(repo.name || '');
            
            // Use avatar URL if available, otherwise use default directly
            let avatarUrl = repo.avatar ? escapeHtml(repo.avatar) : DEFAULT_AVATAR_URL;

            // Build email and phone sections conditionally
            let emailSection = repo.email
                ? `<p class="text-nowrap m-0"><i class="far fa-envelope mr-1"></i> ${escapeHtml(repo.email)}</p>`
                : "";
            let phoneSection = repo.phone
                ? `<p class="text-nowrap m-0"><i class="fas fa-phone mr-1"></i> ${escapeHtml(repo.phone)}</p>`
                : "";

            let $container = $(`<div class="media">
                                    <img src="${avatarUrl}" class="size-50 img-circle mr-3" alt="User Avatar" onerror="this.src='${DEFAULT_AVATAR_URL}'">
                                    <div class="media-body">
                                        <h3 class="dropdown-item-title">
                                            ${name}
                                        </h3>
                                        ${emailSection}
                                        ${phoneSection}
                                    </div>
                                </div>`);
            return $container;
        }

        let formatRepoSelection = (repo) => {
            if (repo.text) {
                return repo.text;
            }

            // Create selected item display
            let name = escapeHtml(repo.name || '');

            let emailSection = repo.email
                ? `<span>${escapeHtml(repo.email)} |</span>`
                : "";

            let phoneSection = repo.phone
                ? `<span>${escapeHtml(repo.phone)}</span>`
                : "";

            let $container = $(`<div class="d-flex align-items-center" style="width: 100%;">
                                    <div class="flex-grow-1 text-center text-truncate" style="min-width: 0;">
                                        <span class="text-truncate d-inline-block" style="max-width: 100%;">${name} | ${emailSection} ${phoneSection}</span>
                                    </div>
                                </div>`);
            return $container || repo.text;
        }
        select.select2({
            language:language,
            width: '100%',
            ajax: {
                url: select.data('url'),
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term,
                        page: params.page || 1,
                        per_page: 20, // Number of items per page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.items,
                        pagination: {
                            more: data.has_more || data.incomplete_results,
                        },
                    };
                },
                cache: true
            },
            minimumInputLength: 0,
            templateResult: formatRepo,
            templateSelection: formatRepoSelection
        });
    }
}

export function selectTasks(selector){
    let select = $(selector);
    let displayId = $(selector).hasClass('show_id')?true:false;

    if (select.length) {
        let formatRepo = (repo) => {
            if (repo.loading) {
                return repo.text;
            }
            let name = escapeHtml(repo.name)
            let $container = displayId?($(`<div><span class="text-bee">${repo.type} #${repo.id}</span>: ${name}</div>`)):$(`<div><span> ${name}</span></div>`);
            return $container;
        }

        let formatRepoSelection = (repo) => {
            if (repo.text) {
                return repo.text;
            }
            let nameSelect = escapeHtml(repo.name)
            let $container = displayId?($(`<div><span class="text-bee">${repo.type} #${repo.id}</span>: ${nameSelect}</div>`)):$(`<div><span>${nameSelect}</span></div>`);
            return $container || repo.text;
        }

        select.select2({
            language:language,
            ajax: {
                url: select.data('url'),
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    let selectThis = $(this),
                        formThis = selectThis.closest('form'),
                        projectId = false;
                    if (formThis.length && formThis.find('[name="project_id"]').length && formThis.find('[name="project_id"]').val()){
                        projectId = formThis.find('[name="project_id"]').val()
                    }
                    return {
                        projectId: projectId,
                        q: params.term,
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.items,
                        // pagination: {
                        //     more: (params.page * 30) < data.total_count
                        // }
                    };
                },
                cache: true
            },
            minimumInputLength: 1,
            templateResult: formatRepo,
            templateSelection: formatRepoSelection
        });
    }
}

export function selectSprints(selector){
    let select = $(selector);

    if (select.length) {
        let formatRepo = (repo) => {
            if (repo.loading) {
                return repo.text;
            }
            let name = escapeHtml(repo.name)
            let $container = ($(`<div><span class="text-bee">${name}</div>`));
            return $container;
        }

        let formatRepoSelection = (repo) => {
            if (repo.text) {
                return repo.text;
            }
            let nameSelect = escapeHtml(repo.name)
            let $container = ($(`<div><span class="text-bee">${nameSelect}</div>`));
            return $container || repo.text;
        }

        select.select2({
            language:language,
            ajax: {
                url: select.data('url'),
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    let selectThis = $(this),
                        formThis = selectThis.closest('form'),
                        projectId = false;
                    if (formThis.length && formThis.find('[name="project_id"]').length && formThis.find('[name="project_id"]').val()){
                        projectId = formThis.find('[name="project_id"]').val()
                    }
                    return {
                        projectId: projectId,
                        q: params.term,
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.items,
                    };
                },
                cache: true
            },
            templateResult: formatRepo,
            templateSelection: formatRepoSelection
        });
    }
}

export function selectUserManager(selector){
    let select = $(selector);

    if (select.length){
        let formatRepo = (repo) => {
            if (repo.loading) {
                return repo.text;
            }
            let name = escapeHtml(repo.name)
            let avatarUrl = repo.avatar ? escapeHtml(repo.avatar) : DEFAULT_AVATAR_URL;
            let $container = $(`<div class="media">
                                    <img src="${avatarUrl}" class="size-50 img-circle mr-3" alt="User Avatar" onerror="this.src='${DEFAULT_AVATAR_URL}'">
                                    <div class="media-body">
                                        <h3 class="dropdown-item-title">
                                            ${name}
                                        </h3>
                                        <p class="text-sm mb-1" style="opacity: 0.8">${repo.position}</p>
                                        <p class="text-nowrap m-0"><i class="far fa-envelope mr-1"></i> ${repo.email}</p>
                                    </div>
                                </div>`);
            return $container;
        }

        let formatRepoSelection = (repo) => {
            if (repo.text) {
                return repo.text;
            }
            let $container = $(`<div class="text-truncate" style="max-width: 100%;">
                                        <span class="text-truncate d-inline-block" style="max-width: 100%;">${repo.name} | ${repo.email}</span>
                                    </div>`);
            return $container || repo.text;
        }

        select.select2({
            language:language,
            ajax: {
                url: select.data('url'),
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        projectId: false,
                        evaluationId: false,
                        q: params.term,
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.items,
                        // pagination: {
                        //     more: (params.page * 30) < data.total_count
                        // }
                    };
                },
                cache: true
            },
            minimumInputLength: 0,
            templateResult: formatRepo,
            templateSelection: formatRepoSelection
        });
    }
}

export function selectUserTaskOfDay(selector, isSearchInModal = null){
    let select = $(selector);

    if (select.length){
        let formatRepo = (repo) => {
            if (repo.loading) {
                return repo.text;
            }
            let name = escapeHtml(repo.name)
            let $container = $(`<div class="media">
                                    <img src="${repo.avatar}" class="size-50 img-circle mr-3">
                                    <div class="media-body">
                                        <h3 class="dropdown-item-title">
                                            ${name}
                                        </h3>
                                        <p class="text-sm mb-1" style="opacity: 0.8">${repo.position}</p>
                                        <p class="text-nowrap m-0"><i class="far fa-envelope mr-1"></i> ${repo.email}</p>
                                    </div>
                                </div>`);
            return $container;
        }

        let formatRepoSelection = (repo) => {
            if (repo.text) {
                return repo.text;
            }
            let $container = $(`<div class="d-flex align-items-center"><img src="`+repo.avatar+`" class="avatar-employees mr-2" alt="User Image">${repo.name}</div>`);
            return $container || repo.text;
        }

        //This issue occurs because Bootstrap modals tend to steal focus from other elements outside of the modal.
        const appendConfig = isSearchInModal ? {dropdownParent: $(isSearchInModal)} : {}
        select.select2({
            language:language,
            ajax: {
                url: select.data('url'),
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        projectId: select.data("project"),
                        evaluationId: false,
                        q: params.term,
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.items,
                        // pagination: {
                        //     more: (params.page * 30) < data.total_count
                        // }
                    };
                },
                cache: true
            },
            ...appendConfig,
            minimumInputLength: 0,
            templateResult: formatRepo,
            templateSelection: formatRepoSelection
        });
    }
}
