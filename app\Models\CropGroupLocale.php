<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CropGroupLocale extends Model
{
    protected $table = 'crop_group_locales';

    protected $fillable = ['crop_group_id', 'language_id', 'name', 'description'];

    /**
     * Get the crop group.
     * @return BelongsTo
     */
    public function cropGroup(): BelongsTo
    {
        return $this->belongsTo(CropGroup::class);
    }

    /**
     * Get the language.
     * @return BelongsTo
     */
    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }

    /**
     * Scope to filter by language ID
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $languageId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForLanguage($query, $languageId)
    {
        return $query->where('language_id', $languageId);
    }
}
