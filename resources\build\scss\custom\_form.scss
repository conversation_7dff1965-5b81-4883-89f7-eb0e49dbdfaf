.rt-2 {
    position: relative;
    top: 2px;
}

label:not(.form-check-label):not(.custom-file-label) {
    font-weight: normal;
}

body[data-locales="ja"] {
    label:not(.form-check-label):not(.custom-file-label) {
        font-weight: normal;
        font-family: "Noto Sans JP", sans-serif;
    }
}

.btn-primary {
    background-color: $bgPrimary;
    border-color: $bgPrimary;
}

.btn-default {
    //background-color: $bgDefault;
    //border-color: $bdDefault;
    background-color: $bgPrimary;
    border-color: $bgPrimary;
    color: #fff;
    &:hover,
    &:focus,
    &:active {
        background-color: #088493;
        border-color: #088493;
        color: #fff;
    }
}

.form-control {
    height: 52px;
    background: #6A707E00 0% 0% no-repeat padding-box;
    border: 1px solid #DDDFE1;
    opacity: 1;
    caret-color: $color-bee;
}

.card-header .form-control {
    height: auto;
}

.form-control:focus {
    border-color: $color-bee;
}

.icheck-bee>input:first-child:checked+label::before,
.icheck-bee>input:first-child:checked+input[type="hidden"]+label::before {
    background-color: $color-bee;
    border-color: $color-bee;
}

.icheck-bee>input:first-child:not(:checked):not(:disabled):hover+label::before,
.icheck-bee>input:first-child:not(:checked):not(:disabled):hover+input[type="hidden"]+label::before {
    border-color: $color-bee;
}

.icheck-primary>input:first-child:checked+label::before,
.icheck-primary>input:first-child:checked+input[type="hidden"]+label::before {
    background-color: transparent;
    border-color: $color-bee;
}

[class*=icheck-]>input[type="radio"]:first-child:checked+input[type=hidden]+label::after,
[class*=icheck-]>input[type="radio"]:first-child:checked+label::after {
    background: #0791A3;
    top: 50%;
    margin-top: -6px;
    margin-left: 5px;
    border-radius: 50%;
    border: 0;
    width: 12px;
    height: 12px;
    transform: none;
}

.card .form-group .select2-container--bootstrap4 .select2-selection--single {
    height: 52px !important;
}

.select2-container--bootstrap4 .select2-selection--multiple {
    min-height: 52px!important;
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
}

// .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
//     line-height: 52px;
// }
.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {
    margin-top: 14px;
    margin-left: 4px;
}
.select2-search {
    display: flex;
    .select2-search__field {
        width: calc(100% - 20px - 16px)!important;
    }
}
.select2-search--dropdown {
    .select2-search__field{
        flex: 1 1 0;
    }
}
textarea.form-control:focus {
    height: auto;
}

#collapseFilter {
    .form-control {
        height: 52px;
    }
    .card .form-group .select2-container--bootstrap4 .select2-selection--single {
        height: 52px !important;
        display: flex;
        align-items: center;
    }
    .select2-container--bootstrap4 .select2-selection--multiple {
        display: flex;
        text-align: center;
        justify-content: center;
        align-items: center;
    }
    .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
        line-height: 27px;
    }
    .select2-container .select2-search--inline .select2-search__field {
        margin-top: 14px;
        padding-left: 4px;
    }
}

.actions-leaderboard {
    .btn {
        line-height: 38px;
    }
}

.input-group-append {
    background-color: #6A707E1A;
}

input::placeholder {
    color: #ABAFB3 !important;
    /* Thay đổi màu placeholder tại đây */
    opacity: 1;
    /* Đảm bảo màu không bị giảm độ trong suốt */
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    margin-top: 5px;
}

#collapseFilter .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    margin-top: 0 !important;
}

.filter-style{
    width: 100%;
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 0 5px;
    margin-bottom: 0px;
    .select2-selection__choice {
        margin-top: 5px;
        margin-bottom: 5px;
    }
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-search__field {
    margin-top:0 !important;
}

body {
    &[data-locales="vi"],  &[data-locales="en"] {
        .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
            margin-top: 9px;
        }
        .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
            top: 30%;
        }
        span.select2.select2-container .button-remove-value-select2 {
            top: 1rem;
        }
    }
}

// Filter badges wrapper - prevent overflow and enable wrapping
.filter-badges-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    align-items: flex-start;
    line-height: 1.4;

    // Ensure badges don't overflow container
    max-width: 100%;
    word-wrap: break-word;

    // Style for individual filter badges
    .badge-filter {
        display: inline-flex;
        align-items: center;
        white-space: normal;
        word-break: break-word;
        max-width: 100%;
        text-align: left;
    }

    // Handle long filter text
    .badge-filter {
        // Allow text to wrap within badges
        white-space: normal;
        height: auto;
        min-height: 1.5rem;

        // Ensure consistent spacing
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
    }
}
