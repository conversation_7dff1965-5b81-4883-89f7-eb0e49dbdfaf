<?php

namespace App\Logics;

use App\Enums\PlotStage;
use App\Enums\TreeStatus;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;

class PlotManager
{
    /**
     * Get plot list
     *
     * @param User $user
     * @param callable|null $callback
     * @param array $pagination
     * @return LengthAwarePaginator
     */
    public function getPlots(User $user, $payload = [])
    {
        //TODO: use $user to check role, admin -> get all, otherwise -> where by user_id.
        $mock = $this->generateMockPlots(100);
        $collection = collect($mock);
        $page = data_get($payload, 'page', 1);
        $perPage = data_get($payload, 'per_page', PER_PAGE);
        $items = $collection->forPage($page, $perPage);
        return new LengthAwarePaginator(
            $items->values(),
            $collection->count(),
            $perPage,
            $page,
        );
    }

    private function generateMockPlots(int $count = 50): array
    {
        $states = [
            [TreeStatus::SEED->value(), TreeStatus::DROUGHT->value()],
            [TreeStatus::GROWING->value()],
            [TreeStatus::FLOWERING->value(), TreeStatus::PEST_INFESTED->value()],
            [TreeStatus::RIPE->value(), TreeStatus::DISEASED->value(),],
        ];
        $plots = [];
        $now = Carbon::now();
        for ($i = 1; $i <= $count; $i++) {
            $startAt = Carbon::create(2025, 9, rand(1, 28));
            $endAt = (clone $startAt)->addMonths(rand(3, 6));

            // 80% sẽ có cây trồng
            $hasPlant = mt_rand(1, 100) <= 80;

            $plantData = null;
            if ($hasPlant) {
                $plantStart = (clone $startAt)->addDays(rand(0, 10));
                $plantEnd = (clone $plantStart)->addDays(rand(30, 90));
                $remainNext = $plantStart->greaterThan($now) ? $plantStart->addDays(rand(5, 15))->diffInSeconds(now(), true) : 0;
                $remainGrownth = $plantEnd->greaterThan($now) ? $plantEnd->addDays(rand(5, 15))->diffInSeconds(now(), true) : 0;
                $plantData = [
                    'id' => $i + 100, // id giả
                    'start_at' => $plantStart->toDateString(),
                    'remain_grownth' => round($remainGrownth),
                    'plant_states' => $states[rand(0, 2)],
                    'grownth_stage' => rand(0, 6),
                    'remain_next_stage' => round($remainNext)
                ];
            }

            $plotStage = $plantData ? PlotStage::PLANTED : [PlotStage::NOT_PLOWED, PlotStage::PLOWED][rand(0, 1)];
            $plots[] = [
                'id' => $i,
                'x' => rand(1, 30),
                'y' => rand(1, 30),
                'stage' => $plotStage->value(),
                'start_at' => $startAt->toDateString(),
                'end_at' => $endAt->toDateString(),
                'plant' => $plantData,
            ];
        }
        return $plots;
    }
}
