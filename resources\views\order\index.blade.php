@extends('layouts.master')
@section('title', __('language.order_management'))
@section('meta')
@stop

@section('css_library')
    @include('partials.style-library', ['datepicker' => true, 'select2' => true])
@stop

@section('css_page')

@stop

@section('header')
    <li class="nav-item">
        {{ __('language.order_management') }}
    </li>
@endsection
@section('content')
    @php
        $request = request();
    @endphp
    <section class="content pt-3">
        @include('partials.breadcrumb', [
            'item' => '<a href="' . route('orders.index') . '">' . __('language.order_management') . '</a>
            &nbsp;/&nbsp;' . __('language.order_list')
        ])
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-end flex-wrap">
                <div class="mr-1 mb-2">
                    <a href="{{route('orders.index',['refresh'=>'true'])}}" class="btn btn-default text-white d-none d-sm-inline-block"><i class="fal fa-sync"></i> {{ __('language.refresh') }}</a>
                    <a href="#collapseFilter" class="btn btn-default text-white" data-toggle="collapse"><i class="far fa-filter"></i> {{ __('language.filter') }}</a>
                </div>
            </div>
            <div id="collapseFilter" class="collapse">
                <div class="card mb-3">
                    <div class="card-body border-0">
                        @include('order.partials.form-filter')
                    </div>
                </div>
            </div>
            @if($is_filter)
                <div class="mb-2">
                    {{ __('language.filter_mode') }}: {!! $is_filter !!}
                </div>
            @endif
            <div class="table-list-data">
                @include('order.partials.list-orders')
            </div>
        </div>
    </section>
@stop

@section('js_library')
    @include('partials.script-library', ['datepicker' => true, 'select2' => true])

@stop

@section('js_page')

@vite(['resources/build/js/common.js'])

@stop
