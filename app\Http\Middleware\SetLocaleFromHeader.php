<?php

namespace App\Http\Middleware;

use App\Models\Language;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class SetLocaleFromHeader
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get Accept-Language header
        $languageCode = $request->header('Accept-Language');

        // Get language from cache or database
        $language = $this->getLanguage($languageCode);

        // Set language_id to request attributes for use in controllers/managers
        $request->attributes->set('language_id', $language->id);
        $request->attributes->set('language_code', $language->code);

        return $next($request);
    }

    /**
     * Get language by code with cache
     * Fallback to 'vi' if not found or not active
     *
     * @param string|null $code
     * @return Language
     */
    private function getLanguage(?string $code): Language
    {
        // If no code provided, use default 'vi'
        if (empty($code)) {
            return $this->getDefaultLanguage();
        }

        // Try to get language from cache - only cache if found
        $cacheKey = "language_active_{$code}";

        $language = Cache::get($cacheKey);

        if ($language === null) {
            $language = Language::byCode($code)->active()->first();

            // Only cache if language exists and is active
            if ($language) {
                Cache::put($cacheKey, $language, 3600);
            }
        }

        // If not found or not active, fallback to 'vi'
        if (!$language) {
            return $this->getDefaultLanguage();
        }

        return $language;
    }

    /**
     * Get default language (vi)
     *
     * @return Language
     */
    private function getDefaultLanguage(): Language
    {
        return Cache::remember('language_default_vi', 3600, function () {
            return Language::byCode(Language::VI)->active()->firstOrFail();
        });
    }
}

