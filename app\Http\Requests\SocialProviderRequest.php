<?php

namespace App\Http\Requests;

use App\Enums\DeviceType;
use App\Enums\SocialProvider;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class SocialProviderRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            /**
             * Provider Social.
             * 
             * @example "google"
             */
            'provider' => ['required', new Enum(SocialProvider::class)],
            /**
             * Access token social.
             * 
             * @example "eyJhbGciOiJSUzI1NiIsImtpZCI6IjA3ZjA3OGYyNjQ..."
             */
            'access_token' => ['required', 'string'],
            /**
             * Device Type (1: IOS, 2: Android, 3: Web).
             * @example "1"
             */
            'device_type' => ['nullable', Rule::enum(DeviceType::class)],
            /**
             * Device <PERSON>ken.
             * @example "e4Vvwx2TS9O:APA91bE8X5F7Vqzj9sy9B2hNQY_0yP5T4QmQd2JjUo4r9r6qk3vK6ZL6mKzM7aP9mQW"
             */
            'device_token' => ['nullable', 'string'],
        ];
    }
}
