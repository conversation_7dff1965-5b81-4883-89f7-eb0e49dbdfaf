<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Add name field to orders table with varchar(500)
            $table->string('name', 500)->nullable()->after('code');

            // Add latest_payment_id column to reference the most recent payment
            $table->uuid('latest_payment_id')->nullable();

            // Add index for better join performance
            $table->index('latest_payment_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Drop index first
            $table->dropIndex(['latest_payment_id']);

            // Remove latest_payment_id column
            $table->dropColumn('latest_payment_id');

            // Remove name field from orders table
            $table->dropColumn('name');
        });
    }
};
