<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Configuration;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Ecdsa\Sha256;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key\InMemory;
use SocialiteProviders\Apple\AppleExtendSocialite;
use SocialiteProviders\Manager\SocialiteWasCalled;
use SocialiteProviders\Zalo\ZaloExtendSocialite;

class EventServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // assign event for socialite for zalo, apple
        $this->app['events']->listen(SocialiteWasCalled::class, AppleExtendSocialite::class);
        $this->app['events']->listen(SocialiteWasCalled::class, ZaloExtendSocialite::class);
        // bind jwt configuration for apple sign in
        $this->app->bind(Configuration::class, fn () => Configuration::forSymmetricSigner(
            Sha256::create(),
            InMemory::plainText(config('services.apple.private_key')),
        ));
    }
}
