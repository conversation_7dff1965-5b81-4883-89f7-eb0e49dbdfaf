<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OtpCodes extends Model
{
    protected $table = 'otp_codes';

    public $timestamps = true;

    protected $guarded = [];

    const PASSWORD_RESET_TOKEN_KEY = 'password_reset_token';
    const LIMIT_SEND_CODE = 5;
    const EXPIRED_TIME_CODE = 5; // minute
    const EXPIRED_TIME_TOKEN = 60; // minute
    const RESET_TIME_SEND_CODE = 60; // minute
}
