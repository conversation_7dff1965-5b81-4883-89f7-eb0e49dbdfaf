<?php

namespace App\Helpers;

use App\Models\Language;
use Illuminate\Support\Facades\App;
use Carbon\Carbon;

class DateTimeHelper
{
    const DATETIME_FORMAT = [
        Language::VI => [
            'date' => 'd/m/Y',
            'regex_date' => '/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/([0-9]{4})$/',
            'month' => 'm/Y',
            'regex_month' => '/^(0[1-9]|1[0-2])\/([0-9]{4})$/',
            'datetime' => 'd/m/Y H:i',
            'regex_datetime' => '/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/([0-9]{4}) ([01]?[0-9]|2[0-3]):([0-5][0-9])$/',
        ],
        Language::EN => [
            'date' => 'Y-m-d',
            'regex_date' => '/^([0-9]{4})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/',
            'month' => 'Y-m',
            'regex_month' => '/^([0-9]{4})-(0[1-9]|1[0-2])$/',
            'datetime' => 'Y-m-d H:i',
            'regex_datetime' => '/^([0-9]{4})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1]) ([01]?[0-9]|2[0-3]):([0-5][0-9])$/',
        ],
        Language::JA => [
            'date' => 'Y年m月d日',
            'regex_date' => '/^([0-9]{4})年(0[1-9]|1[0-2])月(0[1-9]|[1-2][0-9]|3[0-1])日$/',
            'month' => 'Y年m月',
            'regex_month' => '/^([0-9]{4})年(0[1-9]|1[0-2])月$/',
            'datetime' => 'Y年m月d日 H時i分',
            'regex_datetime' => '/^([0-9]{4})年(0[1-9]|1[0-2])月(0[1-9]|[1-2][0-9]|3[0-1])日 ([01]?[0-9]|2[0-3])時([0-5][0-9])分$/',
        ],
    ];

    /**
     * Convert date format between languages
     */
    public static function convertToLanguage($date, $from, $to)
    {
        // Convert date format
        if (preg_match(self::DATETIME_FORMAT[$from]['regex_date'], $date)) {
            return Carbon::createFromFormat(self::DATETIME_FORMAT[$from]['date'], $date)
                ->format(self::DATETIME_FORMAT[$to]['date']);
        }
        // Convert month format
        if (preg_match(self::DATETIME_FORMAT[$from]['regex_month'], $date)) {
            return Carbon::createFromFormat(self::DATETIME_FORMAT[$from]['month'], $date)
                ->format(self::DATETIME_FORMAT[$to]['month']);
        }
        // Convert datetime format
        if (preg_match(self::DATETIME_FORMAT[$from]['regex_datetime'], $date)) {
            return Carbon::createFromFormat(self::DATETIME_FORMAT[$from]['datetime'], $date)
                ->format(self::DATETIME_FORMAT[$to]['datetime']);
        }

        return $date;
    }

    /**
     * Change format date strtotime
     */
    public static function formatLanguage($date, $type = 'date')
    {
        return Carbon::parse($date)->format(self::DATETIME_FORMAT[App::getLocale()][$type]);
    }
}
