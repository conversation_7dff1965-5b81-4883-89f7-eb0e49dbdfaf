@extends('layouts.master')
@section('title',__('language.crops_management'))
@section('meta')
@stop

@section('css_library')
    @include('partials.style-library', ['datepicker' => true, 'fancybox' => true, 'select2' => true, 'icheck' => true])
@stop

@section('css_page')
@stop

@section('header')
    <li class="nav-item">
        {{ __('language.crops_management') }}
    </li>
@endsection
@section('content')
    @php
        $request = request();
    @endphp
    <section class="content pt-3">
        @include('partials.breadcrumb', [
            'item' => '<a href="' . route('crops.index') . '">' . __('language.crops_management') . '</a>
            &nbsp;/&nbsp;' . __('language.crops_list')
        ])
        <div class="container-fluid">
            @include('crops.partials.form-filter')
            @if($isFilter)
                <div class="mb-2">
                    {{ __('language.filter_mode') }}: {!! $isFilter !!}
                </div>
            @endif
            <div class="table-list-data">
                @include('crops.partials.list-crops')
            </div>
        </div>
    </section>
@stop

@section('js_library')
    @include('partials.script-library', ['datepicker' => true, 'fancybox' => true, 'select2' => true])
@stop

@section('js_page')
@stop