@if (isset($orders) && count($orders) > 0)
<div class="table-projects table-responsive" id="double-scroll">
    <table class="table table-hover table-valign-middle min-width-1200">
        <thead class="text-center text-nowrap">
        <tr>
            <th>{{ __('language.order_code') }}</th>
            <th>{{ __('language.order_type') }}</th>
            <th>{{ __('language.customer') }}</th>
            <th>{{ __('language.order_content') }}</th>
            <th>{{ __('language.amount_money') }}</th>
            <th>{{ __('language.status') }}</th>
            <th>{{ __('language.payment_channel') }}</th>
            <th>{{ __('language.created_date') }}</th>
            <th>{{ __('language.completed_date') }}</th>
            <th width = "120px">{{ __('language.action') }}</th>
        </tr>
        </thead>
        <tbody>
            @foreach($orders as $idx => $order)
                <tr class="">
                    <td class="">{{$order->code}}</td>
                    <td class="">
                        <span>{{ $order->type->label() }}</span>
                    </td>
                    <td>
                        @if($order->user)
                            <div class="d-flex align-items-center ">
                                @php
                                    // Use User model's avatarUrl
                                    $avatarUrl = $order->user->avatarUrl('sm');
                                @endphp
                                <img src="{{ $avatarUrl }}"
                                     alt="{{ $order->user->name }}"
                                     class="rounded-circle me-2"
                                     width="32"
                                     height="32"
                                     style="object-fit: cover;"
                                     onerror="this.src='{{ asset('images/user-default.png') }}'">
                                <span class="pl-1">{{$order->user->name}}</span>
                            </div>
                        @endif
                    </td>
                    <td>
                        <div class="">
                        @if($order->name)
                            {{$order->name}}
                        @endif
                        </div>
                    </td>
                    <td class="">
                        {{number_format($order->amount)}} {{$order->currency}}
                    </td>
                    <td class="">
                        <span class="">{{ $order->status->label() }}</span>
                    </td>
                    <td class="">
                        @if($order->latestPayment && $order->latestPayment->channel)
                            <span>{{ $order->latestPayment->channel->label() }}</span>
                        @endif
                    </td>
                    <td class="text-center text-nowrap">
                        {{ \App\Helpers\DateTimeHelper::formatLanguage($order->created_at, 'datetime') }}
                    </td>
                    <td class="text-center text-nowrap">
                        @if($order->paid_at)
                            {{ \App\Helpers\DateTimeHelper::formatLanguage($order->paid_at, 'datetime') }}
                        @endif
                    </td>
                    <td class="text-center text-nowrap">
                        {{-- <a href="#" data-toggle='tooltip' title="Xem chi tiết" class="text-md text-info mr-2"><i class="far fa-eye"></i></a>
                        @if($order->status->value === 'pending')
                            <a href="#" data-toggle='tooltip' title="Hủy đơn hàng" class="text-md text-danger"><i class="far fa-times-circle"></i></a>
                        @endif --}}
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
<div class="pb-4">
    {{ $orders->appends($request->query())->links('partials.pagination') }}
</div>
@else
    @include('partials.no-data-found')
@endif

