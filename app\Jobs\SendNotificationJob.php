<?php

namespace App\Jobs;

use App\Models\UserDevice;
use App\Services\FCMService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const CHUNK_SIZE = 500;

    protected array $userIds;
    protected string $title;
    protected string $body;
    protected array $data;

    /**
     * Create a new job instance.
     */
    public function __construct(array $userIds, string $title, string $body, array $data = [])
    {
        $this->userIds = $userIds;
        $this->title = $title;
        $this->body = $body;
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(FCMService $fcmService): void
    {
        UserDevice::select(['id', 'device_token'])
            ->whereIn('user_id', $this->userIds)
            ->chunkById(self::CHUNK_SIZE, function ($devices) use ($fcmService) {
                $tokens = $devices->pluck('device_token')->toArray();
                if (!empty($tokens)) {
                    $fcmService->sendNotification(
                        $tokens,
                        ['title' => $this->title, 'body' => $this->body],
                        $this->data
                    );
                }
            });
    }
}
