<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CropLocale extends Model
{
    protected $table = 'crop_locales';

    protected $fillable = [
        'crop_id',
        'language_id',
        'name',
        'description',
    ];

    /**
     * Get the crop.
     * @return BelongsTo
     */
    public function crop(): BelongsTo
    {
        return $this->belongsTo(Crop::class, 'crop_id');
    }

    /**
     * Get the language.
     * @return BelongsTo
     */
    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class, 'language_id');
    }

    /**
     * Scope to filter by language ID
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $languageId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForLanguage($query, $languageId)
    {
        return $query->where('language_id', $languageId);
    }
}
