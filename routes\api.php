<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\CheckVersionController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Auth\PasswordController;
use App\Http\Controllers\Api\CropController;
use App\Http\Controllers\Api\CropGroupController;
use App\Http\Controllers\Api\PlotController;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Api', 'as'=>'api.'], function () {
    Route::group(['prefix' => 'auth', 'as'=>'auth.'], function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('/login-social', [AuthController::class, 'loginSocial']);
        Route::post('logout', [AuthController::class, 'logout'])->middleware('auth:api');
        Route::post('refresh', [AuthController::class, 'refresh'])->middleware('auth:api');
        Route::post('send-code', [AuthController::class, 'sendCode'])->name('send-code');
        Route::post('verify-code', [AuthController::class, 'verifyCode'])->name('verify-code');
        Route::post('reset-password', [AuthController::class, 'resetPassword'])->name('reset-password');
        Route::post('register', [AuthController::class, 'register']);
        Route::put('password', [PasswordController::class, 'update'])->name('password.update');
    });

    // Check version
    Route::post('check-version', [CheckVersionController::class, 'checkVersion'])->name('check.version');

    // Crops
    Route::get('crops', [CropController::class, 'list']);
    // Crop Groups
    Route::get('crop-groups', [CropGroupController::class, 'list']);

    Route::group(['middleware' => 'auth:api'], function () {
        // Payment
        Route::post('/payments/start', [PaymentController::class, 'start']);

        // Order
        Route::post('/orders', [OrderController::class, 'store']);

        // Notification
        Route::post('notifications/send', [NotificationController::class, 'send']);//TODO: remove after tested
        Route::get('notifications/{userId}', [NotificationController::class, 'list']);
        Route::post('notifications/{userId}/read', [NotificationController::class, 'readNotifications']);

        //Land Plot
        Route::get('plots', [PlotController::class, 'list']);
    });
});
