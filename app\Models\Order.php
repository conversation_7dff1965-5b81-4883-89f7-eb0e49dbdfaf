<?php

namespace App\Models;

use App\Enums\OrderStatus;
use App\Enums\OrderType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Order extends Model
{
    use HasUuids;

    protected $fillable = [
        'user_id',
        'type',
        'code',
        'name',
        'amount',
        'currency',
        'status',
        'meta',
        'paid_at',
        'latest_payment_id'
    ];

    protected $casts = [
        'type'    => OrderType::class,
        'status'  => OrderStatus::class,
        'meta'    => 'array',
        'paid_at' => 'datetime',
    ];

    public function payments() {
        return $this->hasMany(Payment::class);
    }

    public function latestPayment() {
        return $this->belongsTo(Payment::class, 'latest_payment_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }


    protected static function booted()
    {
        static::creating(function ($order) {
            if (empty($order->code)) {
                $order->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Update latest_payment_id when a new payment is created
     */
    public function updateLatestPaymentId()
    {
        $latestPayment = $this->payments()
            ->orderBy('created_at', 'desc')
            ->first();

        if ($latestPayment) {
            $this->update(['latest_payment_id' => $latestPayment->id]);
        }
    }

    public static function generateUniqueCode(): string
    {
        do {
            $date = now()->format('ymd');
            $random = Str::upper(Str::random(8));
            $code = 'ORD' . $date . $random;
        } while (self::where('code', $code)->exists());

        return $code;
    }
}
