<?php

namespace App\Enums;

use App\Traits\WithInteger;

enum PlotStage: string
{
    use WithInteger;

    case NOT_PLOWED = "0";
    case PLOWED = "1";
    case PLANTED = "2";

    public function label(): string
    {
        return match ($this) {
            self::NOT_PLOWED => __('language.not_plowed'),
            self::PLOWED => __('language.plowed'),
            self::PLANTED => __('language.planted'),
        };
    }
}
