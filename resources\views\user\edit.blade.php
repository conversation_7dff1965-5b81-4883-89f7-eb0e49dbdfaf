@extends('layouts.master')
@section('title', trans('language.edit_profile'))
@section('header')
    <li class="nav-item d-none d-sm-inline-block">
        {{ trans('language.user_list') }}
    </li>
@endsection
@section('meta')
@stop

@section('css_library')
    @include('partials.style-library', ['datepicker' => true, 'select2' => true, 'icheck' => true])
@stop

@section('css_page')
@stop

@section('content')
    <section class="content pb-4 mt-3">
        @include('partials.breadcrumb', [
            'item' => '<a href="' . route('user.index') . '">' . trans('language.user_management') . '</a>
            &nbsp;/&nbsp;' . trans('language.edit_profile')
        ])
        <div class="scroll-nav__profile">
            <ul>
                <li>
                    <a class="scrollTo" href="#basic_information">
                        {{ trans('language.user_basic_information') }}
                    </a>
                </li>
                <li>
                    <a class="scrollTo" href="#personal_information">
                        {{ trans('language.user_personal_information') }}
                    </a>
                </li>

            </ul>
        </div>
        <div class="container-fluid">
            @if ( Route::currentRouteName() == 'user.create' )
                @include('user.partials.form-user-information', [
                    'action' => route('user.store'),
                ])
            @else
                    @include('user.partials.form-user-information', [
                    'user' => $user,
                    'action' => route('user.update', ['user' => $user]),
                    'method' => 'PUT',
                ])
            @endif
        </div>
    </section>
@stop

@section('js_library')
    @include('partials.script-library', ['datepicker' => true, 'select2' => true])
@stop

@section('js_page')
    @vite(['resources/build/js/pages/user/info.js'])
    <script type="text/javascript">
        $(".scrollTo").on('click', function(e) {
            e.preventDefault();
            let target = $(this).attr('href');
            $('html, body').animate({
                scrollTop: ($(target).offset().top)
            }, 1000);
        });
    </script>
@stop
