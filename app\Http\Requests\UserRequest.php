<?php

namespace App\Http\Requests;

use App\Enums\UserGender;
use App\Models\Role;
use App\Models\User;
use App\Rules\UserRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rule;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => ['required', ...UserRules::NAME],
            'email' => [
                'required',
                Rule::unique('users', 'email')->ignore($this->user),
                ...UserRules::EMAIL
            ],
            'prefecture_id' => [
                'nullable',
                Rule::exists('provinces', 'province_code')
            ],
            'district_id' => [
                'nullable',
                Rule::exists('wards', 'ward_code')
            ],
            'address' => UserRules::ADDRESS,
            'password' => ['nullable', Password::defaults()],
            'birthday' => ['nullable', ...UserRules::BIRTHDAY],
            'phone' => [
                'nullable',
                Rule::unique('users', 'phone')->ignore($this->user),
                ...UserRules::PHONE
            ],
            'avatar' => UserRules::AVATAR,
            'gender' => [Rule::enum(UserGender::class)],
        ];

        // If the user is the last system manager, cannot remove this role
        if ($this->user && $this->user == auth()->id()
            && User::role(Role::ROLE_SYSTEM_MANAGER)->count() == 1
            && !in_array(Role::ROLE_SYSTEM_MANAGER, $this->role)
        ) {
            $rules['role'] = function ($attribute, $value, $fail) {
                return $fail(trans('validation.custom.required_exist_system_manager_user'));
            };
        } else {
            // If the user is a system manager, they cannot change their role
            $isAdmin = auth()->user()->hasRole(Role::ROLE_SYSTEM_MANAGER);
            $allowedRoles = Role::select('name')
                ->when(!$isAdmin, function ($q) {
                    $q->where('name', '!=', Role::ROLE_SYSTEM_MANAGER);
                })
                ->pluck('name')
                ->toArray();
            $rules['role'] = 'array|in:"",' . implode(",", $allowedRoles);
        }

        return $rules;
    }
}
