<?php

namespace App\Http\Requests;

use App\Enums\IdentifierType;
use App\Enums\RouteType;
use App\Helpers\StringHelper;
use App\Models\User;
use App\Rules\UserRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class RegisterUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $raw = trim((string) $this->input('identifier'));
        $isEmail = (new StringHelper)->detectIdentifierType($raw) == IdentifierType::EMAIL->value;
        $emailRule = [
            Rule::unique(User::class, 'email'),
            ...UserRules::EMAIL,
        ];
        $phoneRule = [
            Rule::unique(User::class, 'phone'),
            ...UserRules::PHONE,
        ];
        return [
            //User name
            'name' => ['required', ...UserRules::NAME],
            //Password: Min 8 character, number, lowcase and uppercase, symbols (123, aA, !@#$%).
            'password' => [
                'required',
                'confirmed',
                Password::defaults()
            ],
            //Email or phone number (unique)
            'identifier' => [
                'required',
                'string',
                ...($isEmail ? $emailRule : $phoneRule)
            ],
            //Get token from api verify code
            'token' => ['required', 'max:255'],
        ];
    }
}
