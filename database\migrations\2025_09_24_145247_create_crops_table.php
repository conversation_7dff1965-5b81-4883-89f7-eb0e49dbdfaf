<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crops', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('crop_group_id')->index();
            $table->json('cost')->nullable();
            $table->json('growth_phases')->nullable();
            $table->json('harvest_phases')->nullable();
            $table->tinyInteger('harvest_type')->unsigned()->default(0);
            $table->string('bundle_key')->nullable()->unique();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crops');
    }
};
