<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plot_assignments', function (Blueprint $t) {
            $t->id();
            $t->unsignedBigInteger('plot_id');
            $t->unsignedBigInteger('user_id');
            $t->date('start_at');
            $t->date('end_at');
            $t->timestamps();

            $t->index('user_id');
            $t->index('plot_id');
            $t->index(['plot_id', 'start_at', 'end_at']);
            $t->index(['plot_id', 'end_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plot_assignments');
    }
};
