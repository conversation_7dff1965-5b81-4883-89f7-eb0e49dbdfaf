export default function ModalChangePassword(){
    $(document).on('submit', '.form-change-password', submitChangePassword);
    $(document).on('hide.bs.modal', '#modalChangePassword', hideModalChangePassword);
}

function submitChangePassword(e){
    e.preventDefault();
    let form = $(this),
        msg = form.find('.alert-message'),
        token = $('meta[name="csrf-token"]').length ? $('meta[name="csrf-token"]').attr('content') : '';

    $.ajax({
        headers: {
            'X-CSRF-TOKEN': token
        },
        url: form.attr('action'),
        type: form.attr('method'),
        dataType: "JSON",
        data: new FormData($(this)[0]),
        contentType: false,
        cache: false,
        processData: false,
        success: function (res) {
            msg.addClass('alert-success show').removeClass('d-none alert-danger').text(res.message);
            setTimeout(function () {
                $('#modalChangePassword').modal('hide');
            }, 2000)
        },
        error: function (xhr) {
            const res = xhr.responseJSON;
            let messages;
            if (xhr.status === 422 && res.errors) {
                messages = Object.keys(res.errors).map(key => {
                    return res.errors[key][0] + "<br />";
                });
            } else {
                messages = res.message;
            }
            msg.addClass('alert-danger show').removeClass('d-none alert-success').html(messages);
        },
        complete: function () {
            $('.form-change-password').find('.btn-primary').prop('disabled', false);
        }
    })
}

function hideModalChangePassword() {
    let modal = $(this);
    $('.form-change-password')[0]?.reset();
    modal.find('.alert-message').removeClass('alert-success alert-danger show').addClass('d-none').text('');
    modal.find('.btn-primary').prop('disabled', false);
}