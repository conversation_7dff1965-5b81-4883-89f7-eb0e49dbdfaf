<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;


class ExceptionHandler
{
    /**
     * Render the exception as an HTTP response.
     */
    public function render(Exception $e, Request $request)
    {
        if ($request->is('api/*')) {
            // Handle API exceptions
            if ($e instanceof AuthenticationException) {
                $statusCode = Response::HTTP_UNAUTHORIZED;
                $response = [
                    'message' => 'Unauthorized'
                ];
            } else if ($e instanceof ValidationException) {
                $msg = '';
                foreach ($e->errors() as $field => $errors) {
                    foreach ($errors as $key => $value) {
                        $msg = $value;
                        break;
                    }
                    if ($msg) break;
                }
                $statusCode = Response::HTTP_UNPROCESSABLE_ENTITY;
                $response = [
                    'message' => $msg
                ];
            } else {
                $statusCode = Response::HTTP_INTERNAL_SERVER_ERROR;
                $response = [
                    'message' => trans('message.server_error')
                ];
            }
            return response()->json($response, $statusCode);
        } else {
            // Handle web exceptions
            if (!$request->isMethod('GET')
                && !$e instanceof AuthenticationException 
                && !$e instanceof ValidationException) {
                return redirect()->back()
                    ->withInput()
                    ->with(['status_failed' => trans('message.server_error')]);
            }
        }
    }
}
