<?php

namespace App\Http\Requests;

use App\Enums\TypeScreen;
use App\Models\OtpCodes;
use App\Models\PasswordReset;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules;

class ResetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'token'    => ['required', 'max:255'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ];
        return $rules;
    }
}
