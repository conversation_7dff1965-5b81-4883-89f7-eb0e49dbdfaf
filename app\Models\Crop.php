<?php

namespace App\Models;

use App\Helpers\StringHelper;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Crop extends Model
{
    use SoftDeletes;

    // Harvest type constants
    const SINGLE = 0;
    const MULTIPLE = 1;

    // Duration limits constants
    const MIN_HARVEST_DURATION = 1;
    const MAX_HARVEST_DURATION = 200;

    const MIN_PHASE_DURATION = 1;
    const MAX_PHASE_DURATION_FIRST = 14;
    const MAX_PHASE_DURATION_NEXT = 200;
    const MAX_PHASE_COUNT_NEXT = 3;

    //Cost
    const COST_GOLD = 'gold';
    const COST_GEM = 'gem';

    const MAX_DESCRIPTION_LENGTH = 10000;

    protected $table = 'crops';

    protected $fillable = [
        'crop_group_id',
        'cost',
        'growth_phases',
        'harvest_phases',
        'harvest_type',
        'bundle_key',
    ];

    protected $casts = [
        'cost' => 'json',
        'growth_phases' => 'json',
        'harvest_phases' => 'json',
        'harvest_type' => 'integer',
    ];

    /**
     * Get the crop group.
     * @return BelongsTo
     */
    public function cropGroup(): BelongsTo
    {
        return $this->belongsTo(CropGroup::class, 'crop_group_id');
    }

    /**
     * Get the crop locales.
     * @return HasMany
     */
    public function locales(): HasMany
    {
        return $this->hasMany(CropLocale::class, 'crop_id');
    }

    /**
     * Get the current locale for the crop based on language ID
     * @param int $languageId
     * @return HasMany
     */
    public function currentLocale(int $languageId): HasMany
    {
        return $this->hasMany(CropLocale::class, 'crop_id')->where('language_id', $languageId);
    }

    /**
     * Scope to include localized data for a specific language
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $languageId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithLocale($query, $languageId)
    {
        return $query->with([
            'locales' => function ($q) use ($languageId) {
                $q->forLanguage($languageId);
            },
            'cropGroup.locales' => function ($q) use ($languageId) {
                $q->forLanguage($languageId);
            }
        ]);
    }

    /**
     * Scope to filter by localized name
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $keyword
     * @param int $languageId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFilterByName($query, $keyword, $languageId)
    {
        $stringHelper = new StringHelper();
        $safeKeyword = $stringHelper->formatStringWhereLike($keyword);

        return $query->whereHas('locales', function ($q) use ($safeKeyword, $languageId) {
            $q->forLanguage($languageId)
                ->where('name', 'like', "%{$safeKeyword}%");
        });
    }

    /**
     * Scope to filter by crop group
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $cropGroupId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFilterByGroup($query, $cropGroupId)
    {
        return $query->where('crop_group_id', $cropGroupId);
    }

    /**
     * Scope to exclude deleted records
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->whereNull('deleted_at');
    }

    /**
     * Get locale data by language code
     * @param string $languageCode
     * @return CropLocale|null
     */
    public function getLocaleByCode(string $languageCode)
    {
        $language = Language::byCode($languageCode)->first();
        if (!$language) {
            return null;
        }

        return $this->locales->where('language_id', $language->id)->first();
    }

    /**
     * Get name by language code
     * @param string $languageCode
     * @return string
     */
    public function getNameByLanguage(string $languageCode): string
    {
        $locale = $this->getLocaleByCode($languageCode);
        return $locale?->name ?? '';
    }

    /**
     * Get description by language code
     * @param string $languageCode
     * @return string
     */
    public function getDescriptionByLanguage(string $languageCode): string
    {
        $locale = $this->getLocaleByCode($languageCode);
        return $locale?->description ?? '';
    }

    /**
     * Get cost as gold value for easier access
     * @return int
     */
    public function getCostGoldAttribute()
    {
        return $this->cost['gold'] ?? 0;
    }

    /**
     * Summary of getGrowthPhasesAttribute
     * @param mixed $value
     * @return array{first: mixed, next: mixed}
     */
    public function getGrowthPhasesAttribute($value)
    {
        $phases = json_decode($value, true) ?? ['first' => [], 'next' => null];

        // Ensure 'first' always exists
        $first = $phases['first'] ?? [];

        // Handle 'next' - có thể null hoặc không tồn tại
        $next = null;
        if (isset($phases['next']) && is_array($phases['next'])) {
            $next = [
                'phases' => $phases['next']['phases'] ?? [],
                'count' => $phases['next']['count'] ?? 1
            ];
        }

        return [
            'first' => $first,
            'next' => $next
        ];
    }
}
