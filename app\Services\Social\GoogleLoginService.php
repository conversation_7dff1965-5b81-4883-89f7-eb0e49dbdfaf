<?php

namespace App\Services\Social;

use Google_Client;
use <PERSON><PERSON>\Socialite\Two\User as SocialiteUser;

class GoogleLoginService
{
    public function verifyIdToken($idToken)
    {
        $client = new Google_Client();

        // Verify the ID token
        $payload = $client->verifyIdToken($idToken);

        // If payload is false, the token is invalid
        if (!$payload) {
            return null;
        }

        // Create a Socialite User instance from the payload
        $socialUser = (new SocialiteUser)
            ->setRaw($payload)
            ->map([
                'id'     => $payload['sub'] ?? null,
                'name'   => $payload['name'] ?? null,
                'email'  => $payload['email'] ?? null,
                'avatar' => $payload['picture'] ?? null,
            ]);

        return $socialUser;
    }
}
