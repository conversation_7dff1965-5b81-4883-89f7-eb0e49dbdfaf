<?php

namespace App\Logics;

use App\Enums\QueueType;
use App\Enums\RouteType;
use App\Enums\SocialProvider;
use App\Enums\TypeScreen;
use App\Exceptions\ErrorException;
use App\Helpers\ImageHelper;
use App\Helpers\StringHelper;
use App\Jobs\SendOptCode;
use App\Models\OtpCodes;
use App\Models\Role;
use App\Models\User;
use App\Models\UserDevice;
use App\Services\AvatarService;
use App\Services\Social\GoogleLoginService;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;
use Tymon\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Configuration;
use Illuminate\Http\Response;

class UserManager
{
    private Configuration $jwtConfig;
    protected $imageHelper;
    protected $stringHelper;
    protected $avatarService;

    public function __construct(
        ImageHelper $imageHelper,
        StringHelper $stringHelper,
        Configuration $jwtConfig,
        AvatarService $avatarService
    ) {
        $this->stringHelper = $stringHelper;
        $this->imageHelper = $imageHelper;
        $this->jwtConfig = $jwtConfig;
        $this->avatarService = $avatarService;
    }

    /**
     * Get all users
     * @param $keyword
     * @param $deleted
     * @return User|\Illuminate\Database\Eloquent\Builder
     */
    public function getUserList(Request $request)
    {
        $users = User::select(
            'users.id',
            'users.avatar',
            'users.name',
            'users.email',
            'users.gender',
            'users.phone',
            DB::raw('CONCAT_WS(", ", users.address, wards.name, provinces.name) AS address'),
            'users.updated_at'
        )
            ->leftJoin('provinces', 'provinces.province_code', 'users.province_code')
            ->leftJoin('wards', 'wards.ward_code', 'users.ward_code')
            ->orderBy('users.id', 'ASC');

        $stringHelper = new StringHelper();
        if (isset($request->id)) {
            $items = array_filter(array_map("trim", explode(",", $request->id)));
            if (!empty($items)) {
                $users->whereIn('users.id', explode(",", $request->id));
            }
        }
        if (isset($request->keyword)) {
            $keyword = $stringHelper->formatStringWhereLike($request->keyword);
            $users->where('users.name', 'LIKE', '%' . $keyword . '%');
        }
        if (isset($request->email)) {
            $email = $stringHelper->formatStringWhereLike($request->email);
            $users->where('email', 'LIKE', '%' . $email . '%');
        }
        if (isset($request->phone)) {
            $phone = $stringHelper->formatStringWhereLike($request->phone);
            $users->where('phone', 'LIKE', '%' . $phone . '%');
        }
        if (isset($request->gender)) {
            $users->whereIn('gender', $request->gender);
        }
        if ($request->has('deleted')) {
            $users = $users->onlyTrashed();
        }

        return $users;
    }

    /**
     * Get the avatar image for a user.
     *
     * @param int $userId
     * @return \Illuminate\Http\Response
     */
    public function getAvatar($userId, $size)
    {
        // Get user avatar image
        $user = User::withTrashed()
            ->select('avatar')
            ->find($userId);

        // If user not found or avatar is empty, return default image
        if (empty($user) || empty($user->avatar) || !Storage::exists($user->avatar)) {
            $defaultImagePath = '/public/images/user-default.png';
            return response()->file(base_path() . $defaultImagePath);
        }

        // If user found and image is not empty, return the the user's image
        $path = AvatarService::pathForSize($user->avatar, $size);
        abort_unless(Storage::exists($path), 404);

        $stream = Storage::readStream($path);
        return response()->stream(function () use ($stream) {
            fpassthru($stream);
        }, 200, ['Content-Type' => 'image/webp']);
    }

    /**
     * Create a new user.
     */
    public function createUser(Request $request, $withRole = false)
    {
        DB::beginTransaction();
        try {
            // Save user to database
            $data = $request->only([
                'name',
                'email',
                'password',
                'phone',
                'prefecture_id',
                'commune_id',
                'address',
                'birthday',
                'gender'
            ]);
            if ($request->filled('name')) {
                $data['name_search'] = (new StringHelper)->transformSearchFullname($data['name']);
            }
            $stringHelper = new StringHelper();
            $data['password'] = Hash::make($request->filled('password') ? $data['password'] : $stringHelper->generatePassword(16));
            if ($request->filled('birthday')) {
                $data['birthday'] = Carbon::parse($request->input('birthday'));
            }
            $user = User::create($data);
            // Store avatar image to storage
            $avatarPath = null;
            if ($request->hasFile('avatar')) {
                $avatarPath = $this->avatarService->updateUserAvatar($user, $request->file('avatar')->getPathname());
            }

            $user->update([
                'avatar' => $avatarPath
            ]);

            // Sync roles
            if ($withRole) {
                $user->syncRoles($request->role);
            }
            DB::commit();

            return $user;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update an existing user.
     */
    public function updateUser(Request $request, $userId)
    {
        $user = User::withTrashed()->findOrFail($userId);
        $canEdit = $user->deleted_at == null
            && auth()->user()->hasAnyRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER]);

        if (!$canEdit) {
            return false;
        }

        DB::beginTransaction();
        try {
            // Update user to database
            $params = [
                'name' => $request->name,
                'name_search' => (new StringHelper)->transformSearchFullname($request->name),
                'email' => $request->email,
                'province_code' => $request->prefecture_id,
                'ward_code' => $request->commune_id,
                'address' => $request->address,
                'birthday' => isset($request->birthday) ? Carbon::parse($request->birthday) : $request->birthday,
                'gender' => $request->gender,
                'phone' => $request->phone,
                // 'language_id' => $request->language,
            ];
            if ($request->has('password') && !empty($request->password)) {
                $params['password'] = bcrypt($request->password);
            }

            // Store avatar image to storage
            $oldAvatarPath = null;
            if ($request->hasFile('avatar')) {
                $oldAvatarPath = $user->avatar;
                $avatarPath = $this->avatarService->updateUserAvatar($user, $request->file('avatar')->getPathname());
                $params['avatar'] = $avatarPath;
            }
            // Update user
            $user->update($params);

            // Sync roles
            $user->syncRoles($request->role);

            DB::commit();

            // Delete old avatar if exists
            if ($oldAvatarPath && Storage::exists($oldAvatarPath)) {
                $oldDir = dirname($oldAvatarPath);
                if (str_starts_with($oldDir, 'avatars/')) {
                    Storage::deleteDirectory($oldDir);
                }
            }

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function deleteUser($userId)
    {
        $user = User::withTrashed()->findOrFail($userId);
        $canDelete = auth()->user()->hasAnyRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER]);

        if (!$canDelete) {
            return false;
        }

        DB::beginTransaction();
        try {
            // Delete user
            $user->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Handle login user for social
     *
     * @throws \Illuminate\Validation\ValidationException
     * @param $request
     * @return \Illuminate\Http\JsonResponse|array|false
     */
    public function loginWithSocial($data)
    {
        try {
            // get data from social provider
            $provider = $data['provider'] ?? '';
            $accessToken = $data['access_token'] ?? null;

            // If provider is google, verify id token with api google endpoint
            try {
                if ($provider === SocialProvider::GOOGLE->value) {
                    $user = (new GoogleLoginService())->verifyIdToken($accessToken);
                } else {
                    // Other prvoviders, call socialite to get user info if provider is not google
                    config()->set('services.apple.client_secret', $this->generateAppleClientSecret());
                    $driver = Socialite::driver($provider)->stateless();
                    $user = $driver->userFromToken($accessToken);
                }
            } catch (\Exception $e) {
                Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
                return false;
            }

            // Log the exception if accessToken is null
            if (!$user || empty($user)) {
                Log::error("Socialite user not found for provider: {$provider}");
                return false;
            }

            $idUserSocial = $user->getId() ?? null;
            $email = $user->getEmail() ?? null;
            $name = $user->getName() ?? $user->getEmail() ?? null;
            $avatar = $user->getAvatar() ?? null;
            $entry = [
                'provider' => $provider,
                'id'   => $idUserSocial,
            ];

            // If user found by social id, login and return token
            $user = User::withTrashed()->whereJsonContains('social_ids', $entry)->first();
            if ($user) {
                // Check if user is deleted
                if ($user->deleted_at !== null) {
                    return false;
                }
                return $this->generateToken($user, $data);
            }

            // If email is not null, try to find user by email
            if ($email != null) {
                $user = User::withTrashed()->where('email', $email)->first();
                // Check if user is deleted
                if ($user && $user->deleted_at !== null) {
                    return false;
                }
            }

            DB::beginTransaction();
            if (!$user) {
                // Handle create user if not exist in DB
                $generatedPassword = (new StringHelper)->generatePassword(16);
                $user = User::create([
                    'name'       => $name,
                    'email'      => $email,
                    'password'   => Hash::make($generatedPassword),
                    'avatar'     => null,
                    'social_ids' => [$entry],
                ]);
                if (!empty($avatar)) {
                    if ($tempPath = $this->imageHelper->storeAvatarFromUrl($avatar ?? null, $user)) {
                        try {
                            // Use AvatarService to process the temp file
                            $avatarService = new AvatarService();
                            $avatarPath = $avatarService->updateUserAvatar($user, $tempPath);
                            $user->avatar = $avatarPath;
                            $user->save();
                        } finally {
                            // Clean up temp file after processing
                            if (file_exists($tempPath)) {
                                unlink($tempPath);
                            }
                        }
                    }
                }
            } else {
                // If user exists with this email, add social_id to their social_ids array
                $socialIds = $user->social_ids ?? [];
                $socialIds[] = $entry;
                $user->social_ids = $socialIds;
                $user->save();
            }

            DB::commit();

            return $this->generateToken($user, $data);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Handle generate JWT token for user
     *
     * @throws \Illuminate\Validation\ValidationException
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateToken($user, $data)
    {
        try {
            DB::beginTransaction();
            // get token from user login
            $accessToken  = auth('api')->login($user);
            $refreshToken = JWTAuth::claims(['refresh' => true])->fromUser($user);

            $deviceToken = data_get($data, 'device_token');
            $deviceType = data_get($data, 'device_type');
            if (!is_null($deviceToken) && !is_null($deviceType)) {
                UserDevice::query()->updateOrCreate(
                    [
                        'device_token' => $deviceToken
                    ],
                    [
                        'user_id' => $user->id,
                        'device_type' => $deviceType,
                        'device_token' => $deviceToken,
                        'status' => UserDevice::STATUS_ONLINE,
                    ]
                );
            }
            DB::commit();
            $data = [
                'access_token' => $accessToken,
                'refresh_token' => $refreshToken,
                'user_id'      => $user->id,
                'name'         => $user->name,
                'language_id'  => $user->language_id,
                'email'        => $user->email,
                'phone'        => $user->phone,
                'roles'        => $user->roles->pluck('name'),
                'expires_in'   => auth('api')->factory()->getTTL() * 60,
            ];
            return $data;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Handle generate jwt client secret for apple login
     *
     * @return \Illuminate\Http\JsonResponse
     */
    private function generateAppleClientSecret()
    {
        // Get 'now' as an immutable Carbon timestamp to avoid accidental mutation.
        $now = CarbonImmutable::now();

        // create token
        $token = $this->jwtConfig->builder()
            ->issuedBy(config('services.apple.team_id'))
            ->issuedAt($now)
            ->expiresAt($now->addHour())
            ->permittedFor('https://appleid.apple.com')
            ->relatedTo(config('services.apple.client_id'))
            ->withHeader('kid', config('services.apple.key_id'))
            ->getToken($this->jwtConfig->signer(), $this->jwtConfig->signingKey());

        return $token->toString();
    }

    /**
     * handle logic send code
     * @param $request
     * @return array
     */
    public function handleSendCode($request)
    {
        $identifier = $request->identifier;
        $type = (new StringHelper)->detectIdentifierType($identifier);
        $user = User::select('id', 'email', 'phone')->where($type, $identifier)->first();
        if (empty($user) && TypeScreen::FORGOT_PASSWORD->value == $request->type_screen) {
            return response()->json([
                'message' => trans('message.not_found_user')
            ], Response::HTTP_NOT_FOUND);
        }
        //check limit send code
        if (!$this->checkLimitSendCode($user->id ?? $identifier, $request->type_screen)) {
            return response()->json([
                'message' => trans('message.limit_send_code', ['minute' => OtpCodes::RESET_TIME_SEND_CODE])
            ], Response::HTTP_TOO_MANY_REQUESTS);
        }
        //create code
        $code = sprintf('%06d', rand(1, 999999));
        $token = null;
        $subject = trans('language.send_code');
        $view = 'mails.mail_send_code';
        // save otp code to DB before send
        $data = [
            'code' => $code,
            'token' => $token,
            'expires_at' => Carbon::now()->addMinutes(OtpCodes::EXPIRED_TIME_CODE),
            'identifier' => $identifier,
            'type' => $type,
            'user_id' => $user ? $user->id : null,
        ];
        OtpCodes::create($data);
        // send otp code via email/sms
        dispatch(new SendOptCode($identifier, $code, $subject, $view))->onQueue(QueueType::QUEUE_SEND_OTP->value);
        return response()->json([
            'message' => trans('message.success'),
        ]);
    }

    /**
     * handle verify code password
     * @param $request
     * @return string
     */
    public function handleVerifyCode($request)
    {
        $identifier = $request->identifier;
        $code = $request->input('code');
        $otpCode = OtpCodes::select('id', 'user_id', 'code', 'token', 'expires_at', 'created_at')
            ->where('identifier', $identifier)
            ->where('code', $code)
            ->whereNull('completed_at')
            ->where('type', (new StringHelper)->detectIdentifierType($identifier))
            ->orderBy('created_at', 'desc')
            ->first();
        if (empty($otpCode) || $otpCode->code !== $code) {
            return response()->json([
                'message' => trans('message.code_invalid')
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
        if ($otpCode->expires_at < Carbon::now()) {
            return response()->json([
                'message' => trans('message.code_expires')
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
        // create token
        $token = hash_hmac('sha256', Str::random(200), OtpCodes::PASSWORD_RESET_TOKEN_KEY);
        $otpCode->token = $token;
        $otpCode->save();
        return response()->json([
            'data' => $token
        ]);
    }

    /**
     * handle reset password
     * @param $request
     * @return array
     */
    public function handleResetPassword($request)
    {
        try {
            $token = $request->token;
            $password = $request->password;
            $otpCode = OtpCodes::select('id', 'user_id', 'code', 'token', 'expires_at', 'created_at', 'identifier')
                ->where('token', $token)
                ->whereNull('completed_at')
                ->where('created_at', '>', Carbon::now()->subMinutes(OtpCodes::EXPIRED_TIME_TOKEN))
                ->first();
            if (empty($otpCode)) {
                return response()->json([
                    'message' => trans('language.failure')
                ], Response::HTTP_NOT_FOUND);
            }
            $type = (new StringHelper)->detectIdentifierType($otpCode->identifier);
            DB::beginTransaction();
            $otpCode->completed_at = Carbon::now();
            $otpCode->save();
            User::where($type, $otpCode->identifier)->update([
                'password' => Hash::make($password)
            ]);
            DB::commit();
            return response()->json([
                'message' => trans('message.success')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * handle check limit send code
     * @param $userId
     * @return bool
     */
    private function checkLimitSendCode($identifier, $typeScreen)
    {
        $count = OtpCodes::whereNull('completed_at')
            ->when($typeScreen == TypeScreen::FORGOT_PASSWORD->value, function ($query) use ($identifier) {
                $query->where('user_id', $identifier);
            }, function ($query) use ($identifier) {
                $query->where('identifier', $identifier);
            })
            ->where('created_at', '>', Carbon::now()->subMinutes(OtpCodes::RESET_TIME_SEND_CODE))
            ->count();

        return $count < OtpCodes::LIMIT_SEND_CODE;
    }

    /**
     * handle register user
     * @param $request
     * @return string
     */
    public function handleRegister($request)
    {
        // Wrap in a transaction for atomicity: validate OTP -> create user -> mark OTP as used -> generate token
        try {
            DB::beginTransaction();
            $token = $request->token;
            $identifier = $request->identifier;
            $type = (new StringHelper)->detectIdentifierType($identifier);
            // validate OTP
            $otpCode = OtpCodes::select('id', 'user_id', 'code', 'token', 'expires_at', 'created_at', 'identifier')
                ->where('identifier', $identifier)
                ->where('token', $token)
                ->whereNull('completed_at')
                ->where('created_at', '>', Carbon::now()->subMinutes(OtpCodes::EXPIRED_TIME_TOKEN))
                ->first();
            if (empty($otpCode)) {
                throw new ErrorException(__('language.failure'), Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            // create user
            $request->replace($request->only(['name', 'password']));
            $request->merge([$type => $identifier]);
            $user = $this->createUser($request);
            // mark OTP as used
            $otpCode->completed_at = Carbon::now();
            $otpCode->save();

            DB::commit();
            // generate token
            return $this->generateToken($user, $request);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update user password
     *
     * @param User $user
     * @param string $newPassword
     * @return bool
     */
    public function updatePassword(User $user, $newPassword)
    {
        return $user->update([
            'password' => Hash::make($newPassword)
        ]);
    }
}
