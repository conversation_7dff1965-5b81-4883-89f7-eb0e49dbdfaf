<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CropGroupResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /**
             * Crop group ID.
             * @var int
             * @example 10
             */
            "id" => $this->id,
            /**
             * Crop group name.
             * @var string
             * @example "Rau ăn lá"
             */
            "name" => $this->name,
            /**
             * Crop group description.
             * @var string
             * @example "Các loại rau ăn lá."
             */
            "description" => $this->description,
            /**
             * Crop group parent ID.
             * @var int
             * @example 1
             */
            "parent_id" => $this->parent_id,
        ];
    }
}
