<?php

namespace App\Services;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use InvalidArgumentException;

class SmsService
{
    // Contract types
    const CONTRACT_TYPE_CSKH = "1";
    const CONTRACT_TYPE_QC = "2";

    // Data coding
    const DATA_CODING_NO_DIACRITICS = "0";
    const DATA_CODING_VIETNAMESE = "8";

    // Fixed values
    const IS_TELCO_SUB = "0";
    const SMS_METHOD_NAME = "send_sms_list";

    // Error codes
    const VNPT_ERROR_RATE_LIMIT = 16;

    // Batch size and retry attempts
    const MAX_BATCH_SIZE = 15;
    const MAX_RETRY_ATTEMPTS = 3;

    // Config properties
    private string $apiUrl;
    private string $labelId;
    private string $contractId;
    private string $agentId;
    private string $apiUser;
    private string $apiPass;
    private string $username;
    private string $otpTemplateId;

    public function __construct()
    {
        $this->apiUrl = config('services.sms.url');
        $this->labelId = config('services.sms.label_id');
        $this->contractId = config('services.sms.contract_id');
        $this->agentId = config('services.sms.agent_id');
        $this->apiUser = config('services.sms.api_user');
        $this->apiPass = config('services.sms.api_pass');
        $this->username = config('services.sms.username');
        $this->otpTemplateId = config('services.sms.otp_template_id');
    }

    /**
     * Send OTP SMS
     *
     * @param string $otp
     * @param array $phones
     * @param array $options
     * @return array ['sent' => bool]
     * @throws ConnectionException
     */
    public function sendOtp(string $otp, array $phones, array $options = []): array
    {
        return $this->sendSms(
            $this->otpTemplateId,
            [["NUM" => "1", "CONTENT" => $otp]],
            $phones,
            $options
        );
    }

    /**
     * Send SMS with template
     *
     * @param string $templateId
     * @param array $params
     * @param array $phones
     * @param array $options
     * @return array ['sent' => bool]
     * @throws ConnectionException
     */
    public function sendSms(string $templateId, array $params, array $phones, array $options = []): array
    {
        $validPhones = $this->validatePhones($phones);

        if (empty($validPhones)) {
            return ['sent' => false, 'message' => __('message.invalid_phone_format')];
        }

        $chunks = array_chunk($validPhones, self::MAX_BATCH_SIZE);
        $allSuccess = true;

        foreach ($chunks as $chunk) {
            $phoneString = implode(',', $chunk);
            $chunkSuccess = false;
            $attempt = 1;

            // Retry 3 times if rate limited
            while ($attempt <= self::MAX_RETRY_ATTEMPTS) {
                // Call VNPT SMS API
                $result = $this->callVnptSmsApi($templateId, $params, $phoneString, $options);

                if ($result['success']) {
                    $chunkSuccess = true;
                    break;
                }

                // Break if not rate limited
                if (!$result['is_rate_limit']) {
                    break;
                }

                sleep(1);
                $attempt++;
            }

            if (!$chunkSuccess) {
                $allSuccess = false;
            }

            // Sleep between chunks
            sleep(1);
        }

        return ['sent' => $allSuccess, 'message' => $allSuccess ? '' :   __('message.sms_api_error')];
    }

    /**
     * Validate phone numbers and convert format
     * Filter out invalid phones and convert 0xxxxxxxxx to 84xxxxxxxxx format
     * @param array $phones
     * @return array Valid phones array
     */
    private function validatePhones(array $phones): array
    {
        $validPhones = [];
        $invalidPhones = [];

        foreach (array_filter($phones) as $phone) {
            if (preg_match('/^84\d{9}$/', $phone)) {
                $validPhones[] = $phone;
                continue;
            }

            if (preg_match('/^0(\d{9})$/', $phone, $matches)) {
                $validPhones[] = '84' . $matches[1];
                continue;
            }

            $invalidPhones[] = $phone;
        }

        if ($invalidPhones) {
            Log::warning('Invalid phone numbers filtered out', [
                'invalid_phones' => $invalidPhones,
                'invalid_count' => count($invalidPhones),
                'valid_count' => count($validPhones)
            ]);
        }

        return $validPhones;
    }

    /**
     * Call VNPT SMS API - third party API call
     * @param string $templateId
     * @param array $params
     * @param string $listPhones
     * @param array $options
     * @return array ['success' => bool]
     * @throws ConnectionException
     */
    private function callVnptSmsApi(string $templateId, array $params, string $listPhones, array $options = []): array
    {
        // Build payload
        $payload = [
            "RQST" => [
                "name" => self::SMS_METHOD_NAME,
                "REQID" => Str::orderedUuid(),
                "LABELID" => (string)$this->labelId,
                "CONTRACTTYPEID" => (string)($options['contractType'] ?? self::CONTRACT_TYPE_CSKH),
                "CONTRACTID" => (string)$this->contractId,
                "TEMPLATEID" => (string)$templateId,
                "PARAMS" => $params,
                'SCHEDULETIME' => $options['scheduleTime'] ?? '',
                'MOBILELIST' => $listPhones,
                'ISTELCOSUB' => (string)self::IS_TELCO_SUB,
                'AGENTID' => (string) $this->agentId,
                'APIUSER' => $this->apiUser,
                'APIPASS' => $this->apiPass,
                'USERNAME' => $this->username,
                'DATACODING' => (string)($options['dataCoding'] ?? self::DATA_CODING_NO_DIACRITICS),
            ]
        ];

        if (!empty($options['saleOrderId'])) {
            $payload['RQST']['SALEORDERID'] = $options['saleOrderId'];
        }

        if (!empty($options['packageId'])) {
            $payload['RQST']['PACKAGEID'] = $options['packageId'];
        }

        // Call API
        $startTime = microtime(true);

        $response = Http::withHeaders([
            'Content-Type' => 'application/json; charset=UTF-8',
        ])->post($this->apiUrl, $payload);

        $duration = round((microtime(true) - $startTime) * 1000, 2);
        $responseData = $response->json();
        $errorCode = (int)($responseData['RPLY']['ERROR'] ?? -1);

        if ($errorCode === 0) {
            return ['success' => true];
        }

        // Log error for debugging
        Log::error('VNPT SMS API call failed', [
            'template_id' => $templateId,
            'params' => $params,
            'list_phones' => $listPhones,
            'options' => $options,
            'duration_ms' => $duration,
            'response' => $responseData
        ]);

        return [
            'success' => false,
            'is_rate_limit' => $errorCode === self::VNPT_ERROR_RATE_LIMIT,
            'message' => __('message.sms_api_error')
        ];
    }
}