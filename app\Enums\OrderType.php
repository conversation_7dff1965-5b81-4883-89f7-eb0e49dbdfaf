<?php

namespace App\Enums;

enum OrderType: string
{
    case RECHARGE_COINS = 'recharge_coins';
    case ORDER_PAYMENT   = 'order_payment';
    // Add more order types here...

    /**
     * Get the label for the order type
     */
    public function label(): string
    {
        return match($this) {
            self::RECHARGE_COINS => __('language.recharge_coins'),
            self::ORDER_PAYMENT => __('language.order_payment'),
        };
    }
}
