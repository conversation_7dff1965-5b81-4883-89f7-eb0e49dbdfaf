<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Illuminate\Support\Str;

class ImageHelper
{
    const USER_DIR = 'users';
    /**
     * Resize the image and constrain aspect ratio
     *
     * @param $imagePath
     * @param $width
     * @param $height
     * @return \Intervention\Image\Image
     */
    public function resizeImage($inputPath, $width, $height, $outputPath = null)
    {
        if (!file_exists($inputPath)) {
            return false;
        }

        // Read the image from the input path
        $img = ImageManager::gd()->read($inputPath);


        // Resize the image while maintaining aspect ratio
        $img->resize($width, $height, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });

        // Get the format of the image
        $format = strtolower(pathinfo($inputPath, PATHINFO_EXTENSION));

        // Encode the image based on the format with high quality
        if ($format === 'png') {
            $img->encodeByExtension('png', 0);
        } else {
            $img->encodeByExtension(null, 100);
        }

        if ($outputPath) {
            // Make directory if not exist
            $directory = dirname($outputPath);
            if (!Storage::exists($directory)) {
                Storage::makeDirectory($directory, 0755, true);
            }

            // Save the image to the specified output path
            $img->save($outputPath);
        }

        return $img;
    }

    /**
     * handle download image from url and store to disk
     * 
     * @param string|null $url
     * @param User $user
     * @param string $disk
     * @return string|null
     */
    public function storeAvatarFromUrl(?string $url, $user): ?string
    {
        if (!$url) return null;
        try {
            $resp = Http::timeout(15)
                ->retry(2, 250)
                ->withHeaders([
                    'User-Agent' => 'AvatarFetcher/1.0',
                    'Accept'     => 'image/*,*/*;q=0.8',
                ])
                ->get($url);

            if (!$resp->ok()) return null;

            $bytes = $resp->body();
            if (!$bytes) return null;

            // Get MIME from header
            $contentType = $resp->header('Content-Type') ?? '';
            $mime = strtolower(strtok($contentType, ';')) ?: null;
            $allowMap = [
                'image/jpeg' => 'jpg',
                'image/jpg'  => 'jpg',
                'image/png'  => 'png'
            ];
            // Max 10mb
            if (strlen($bytes) > 10 * 1024 * 1024) return null;
            $ext = null;

            if ($mime && isset($allowMap[$mime])) {
                $ext = $allowMap[$mime];
            }
            if (!$ext) {
                $pathInUrl = strtolower(parse_url($url, PHP_URL_PATH) ?? '');
                if (preg_match('/\.(jpeg|png)(\?.*)?$/', $pathInUrl, $m)) {
                    $ext = $m[1] === 'jpeg' ? 'jpg' : $m[1];
                }
            }

            // Using finfo to detect MIME from bytes
            if (!$ext && function_exists('finfo_open')) {
                $finfo = new \finfo(FILEINFO_MIME_TYPE);
                $detected = $finfo->buffer($bytes) ?: null;
                if ($detected && isset($allowMap[$detected])) {
                    $ext = $allowMap[$detected];
                }
            }

            if (!$ext) return null;

            // Create temp file
            $tempPath = sys_get_temp_dir() . '/avatar_temp_' . Str::random(25) . '.' . $ext;

            // Save bytes to temp file
            file_put_contents($tempPath, $bytes);

            return $tempPath;
        } catch (\Throwable $e) {
            Log::error($e->getMessage() . "\n" . $e->getTraceAsString());
            return null;
        }
    }

}
