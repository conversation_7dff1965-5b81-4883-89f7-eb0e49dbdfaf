<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('Abcd!234')
        ]);
        $user->syncRoles(Role::ROLE_SYSTEM_MANAGER);
    }
}
