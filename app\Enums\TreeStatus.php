<?php

namespace App\Enums;

use App\Traits\WithInteger;

enum TreeStatus: string
{
    use WithInteger;
        // life cycle
    case SEED       = "0";
    case GERMINATED = "1";
    case SEEDLING   = "2";
    case GROWING    = "3";
    case FLOWERING  = "4";
    case FRUITING   = "5";
    case RIPE       = "6";
    case HARVESTED  = "7";
        //weaken/end
    case WILTING    = "8";
    case DEAD       = "9";
        //abnormal
    case PEST_INFESTED = "10";
    case WEEDY         = "11";
    case DISEASED      = "12";
    case DROUGHT       = "13";
    case FLOODED       = "14";

    public function label(): string
    {
        return match ($this) {
            // life cycle
            self::SEED       => __('language.seed'),
            self::GERMINATED => __('language.germinated'),
            self::SEEDLING   => __('language.seedling'),
            self::GROWING    => __('language.growing'),
            self::FLOWERING  => __('language.flowering'),
            self::FRUITING   => __('language.fruiting'),
            self::RIPE       => __('language.ripe'),
            self::HARVESTED  => __('language.harvested'),

            //weaken/end
            self::WILTING    => __('language.wilting'),
            self::DEAD       => __('language.dead'),

            //abnormal
            self::PEST_INFESTED => __('language.pest_infested'),
            self::WEEDY         => __('language.weedy'),
            self::DISEASED      => __('language.diseased'),
            self::DROUGHT       => __('language.drought'),
            self::FLOODED       => __('language.flooded'),
        };
    }
}
