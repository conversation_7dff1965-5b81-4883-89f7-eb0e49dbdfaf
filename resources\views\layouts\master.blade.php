<!doctype html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title')</title>
    <link rel="icon" href="{{asset('images/logo.ico')}}" type="image/x-icon" />
    @yield('meta')
    <!-- Google Font: Roboto Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,200,300,400,500,600,700,800,900&subset=vietnamese">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{asset('vendor/fontawesome.5.14.0/css/all.css')}}">
    <!-- overlayScrollbars -->
    <link rel="stylesheet" href="{{asset('plugins/overlayScrollbars/css/OverlayScrollbars.min.css')}}">
    <!-- import CSS Library -->
    @yield('css_library')
    <!-- Theme style -->
    {{-- <link rel="stylesheet" href="{{mix('assets/adminlte.css')}}">
    <link rel="stylesheet" href="{{mix('assets/common.css')}}"> --}}
    @vite(['resources/build/scss/adminlte.scss'])
    @vite(['resources/build/scss/common.scss'])
    <link rel="stylesheet" href="{{asset('plugins/sweetalert2/sweetalert2.min.css')}}">
    <link rel="stylesheet" href="{{asset('plugins/toastr/toastr.min.css')}}">
    <link rel="stylesheet" href="{{asset('plugins/tooltipster/css/tooltipster.bundle.css')}}">
    <!-- Page style -->
    @yield('css_page')
</head>
<body class="hold-transition sidebar-mini layout-fixed" data-locales="{{app()->getLocale()}}">
<div class="wrapper">
    @include('partials.header')
    @include('partials.sidebar')
    <div class="content-wrapper">
        @yield('content')
    </div>
    @include('partials.footer')
    @include('partials.modal-change-password')
    <div id="sidebar-overlay"></div>
</div>


<!-- jQuery -->
<script src="{{asset('plugins/jquery/jquery.min.js')}}"></script>
<!-- Bootstrap 4 -->
<script src="{{asset('plugins/bootstrap/js/bootstrap.bundle.min.js')}}"></script>
<!--sweetalert2-->
<script src="{{asset('plugins/sweetalert2/sweetalert2.all.min.js')}}"></script>
<!-- overlayScrollbars -->
<script src="{{asset('plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js')}}"></script>
<!-- overlayScrollbars -->
<script src="{{asset('plugins/sortablejs/Sortable.min.js')}}"></script>
<!-- import JS Library -->
<script src="{{asset('plugins/jquery-validation/jquery.validate.min.js')}}"></script>
<!-- toastr -->
<script src="{{asset('plugins/toastr/toastr.min.js')}}"></script>
<script src="{{asset('plugins/tooltipster/js/tooltipster.bundle.js')}}"></script>
<!-- firebase -->
<script src="https://www.gstatic.com/firebasejs/8.3.2/firebase.js"></script>

@yield('js_library')
<!-- AdminLTE App -->
{{-- <script src="{{mix('assets/AdminLTE.js')}}"></script>
<script src="{{mix('assets/pages/projects/doubleScroll.js')}}"></script>
<script src="{{mix('assets/common.js')}}"></script> --}}
 @vite(['resources/build/js/AdminLTE.js'])
 @vite(['resources/build/js/common.js'])
<script type="text/javascript">
    $('.tooltipster').tooltipster({
        contentCloning: true,
        animation: 'fade',
        delay: 200,
        arrow: false,
        side: 'right'
    });
</script>
<!-- Page script -->
@yield('js_page')

<script type="module">
    // Show alert
    @if(session('status_succeed'))
        toastr.success('{{session('status_succeed')}}', {timeOut: 5000})
    $('.toast-success').click(function () {
        @if(session('task_id'))
            window.location.href = '{{route('task.show',['id'=>session('task_id')])}}'
        @endif
    });
    @elseif(session('status_failed'))
        toastr.error('{{session('status_failed')}}', {timeOut: 5000})
    @endif

    @if ($errors->any())
        toastr.error('{{$errors->first()}}', {timeOut: 5000})
    @endif
</script>
</body>
</html>
