<?php

namespace App\Http\Requests;

use App\Enums\IdentifierType;
use App\Enums\TypeScreen;
use App\Helpers\StringHelper;
use App\Models\User;
use App\Rules\UserRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class SendOtpRequest extends FormRequest
{
    private const ROUTE_VERIFY_NAME = 'api.auth.verify-code';
    private const ROUTE_VERIFY_URI  = 'api/auth/verify-code';

    private const ROUTE_SEND_NAME = 'api.auth.send-code';
    private const ROUTE_SEND_URI  = 'api/auth/send-code';

    private const SEND_CODE = 'send_code';
    private const VERIFY_CODE = 'verify_code';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $raw = trim((string) $this->input('identifier'));
        $context = $this->detectContext();
        $isEmail = (new StringHelper)->detectIdentifierType($raw) === IdentifierType::EMAIL->value;
        $isRegister = $this->input('type_screen') === TypeScreen::REGISTER->value;
        $identifierRules = [
            'bail',
            'required',
            'string',
            'max:255',
            ...($isEmail ? UserRules::EMAIL : UserRules::PHONE),
        ];
        if ($isRegister) {
            $identifierRules[] = Rule::unique(User::class, $isEmail ? 'email' : 'phone');
        }

        $base = [
            'identifier' => $identifierRules,
        ];
        return match ($context) {
            self::VERIFY_CODE => $base + [
                'code' => ['required', 'string', 'max:6'],
            ],
            self::SEND_CODE => $base + [
                'type_screen' => ['required', new Enum(TypeScreen::class)],
            ],
            default => [],
        };
    }

    /**
     * Detect the context of the request based on the route name or URI.
     *
     * @return string
     */
    private function detectContext(): string
    {
        if ($this->routeIs(self::ROUTE_VERIFY_NAME) || $this->is(self::ROUTE_VERIFY_URI)) {
            return self::VERIFY_CODE;
        }

        if ($this->routeIs(self::ROUTE_SEND_NAME) || $this->is(self::ROUTE_SEND_URI)) {
            return self::SEND_CODE;
        }

        return '';
    }
}
