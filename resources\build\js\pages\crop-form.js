import Alpine from "alpinejs";
import { trans } from "../locales/locales.js";
window.Alpine = Alpine;

// Constants for configuration
const MAX_NEXT_PHASE_ITEMS = 3;
const MIN_HARVEST_DURATION = 1;
const MAX_HARVEST_DURATION = 200;
const MIN_PHASE_DURATION = 1;
const MAX_PHASE_DURATION_FIRST = 14;
const MAX_PHASE_DURATION_NEXT = 200;
// Crop constants
const HARVEST_SINGLE = 0;
const HARVEST_MULTIPLE = 1;

Alpine.data("cropForm", (oldData = {}, errors = {}, cropData = null, availableLanguages = []) => ({
    // Labels configuration - sử dụng JS locales cho default values (flat structure)
    labels: {
        harvestFirst: trans('harvest_first'),
        harvestNext: trans('harvest_next'),
        growthFirst: trans('growth_first'),
        growthNext: trans('growth_next')
    },

    // Available languages
    availableLanguages,

    // Form data structure
    data: {},

    // Validation errors
    errors,

    // Form state
    isEdit: false,
    isLoading: false,

    init() {
        // Initialize form data
        this.initializeFormData(oldData, cropData);

        // Set default values for phases
        this.setDefaultValues();

        // Watch harvest_type changes to show/hide next phases
        this.$watch('data.harvest_type', (value) => {
            this.handleHarvestTypeChange(value);
        });

        // Watch first harvest duration for auto-generate growth phases
        this.$watch('data.harvest_phases[0].duration', (duration) => {
            if (duration && duration >= MIN_HARVEST_DURATION && duration <= MAX_HARVEST_DURATION) {
                this.autoGenerateGrowthPhases(parseInt(duration));
            }
        });

        this.$nextTick(() => {
            this.initializeSelect2();
        });

        console.log('🚀 Crop Form initialized');
        console.log('Old Data:', oldData);
        console.log('Crop Data:', cropData);
        console.log('Errors:', errors);
        console.log('Final Form Data:', this.data);
    },

    // Initialize form data from oldData or cropData
    initializeFormData(oldData, cropData) {
        // Create language codes object for multilingual support
        const languageCodes = {};
        this.availableLanguages.forEach(lang => {
            languageCodes[lang.code] = {
                name: oldData[lang.code]?.name || cropData?.locales?.find(l => l.language_id === lang.id)?.name || '',
                description: oldData[lang.code]?.description || cropData?.locales?.find(l => l.language_id === lang.id)?.description || ''
            };
        });

        this.data = {
            // Multilingual data
            ...languageCodes,

            // Core crop data
            crop_group_id: oldData.crop_group_id || cropData?.crop_group_id || null,
            cost: {
                gold: oldData.cost?.gold || cropData?.cost?.gold || null,
                gem: oldData.cost?.gem || cropData?.cost?.gem || null
            },
            harvest_type: oldData.harvest_type !== undefined ? parseInt(oldData.harvest_type) : (cropData?.harvest_type !== undefined ? parseInt(cropData.harvest_type) : HARVEST_SINGLE),
            harvest_phases: oldData.harvest_phases || cropData?.harvest_phases || [
                { name: '', duration: null }
            ],
            growth_phases: {
                first: oldData.growth_phases?.first || cropData?.growth_phases?.first || [
                    { name: '', duration: null }
                ],
                next: {
                    phases: oldData.growth_phases?.next?.phases || cropData?.growth_phases?.next?.phases || [
                        { name: '', duration: null }
                    ],
                    count: oldData.growth_phases?.next?.count || cropData?.growth_phases?.next?.count || 1
                }
            }
        };

        // Detect edit mode
        const methodInput = document.querySelector('input[name="_method"][value="PUT"]');
        this.isEdit = !!methodInput;
    },

    setDefaultValues() {
        this.data.harvest_phases = this.initializeHarvestPhases(this.data.harvest_type);

        if (!this.data.growth_phases.first[0].name) {
            this.data.growth_phases.first[0].name = this.getGrowthPhaseDefaultName('first', 0);
        }

        if (!this.data.growth_phases.next.phases[0].name) {
            this.data.growth_phases.next.phases[0].name = this.getGrowthPhaseDefaultName('next', 0);
        }
    },

    // Get harvest phase name based on index
    getHarvestPhaseName(index) {
        if (index === 0) {
            return this.labels.harvestFirst;
        } else {
            return this.labels.harvestNext;
        }
    },

    // Get growth phase default name
    getGrowthPhaseDefaultName(group, index) {
        if (group === 'first') {
            return `${this.labels.growthFirst} ${index + 1}`;
        } else {
            return `${this.labels.growthNext} ${index + 1}`;
        }
    },

    // Initialize harvest phases with proper names
    initializeHarvestPhases(harvestType) {
        const intType = parseInt(harvestType);

        if (intType === HARVEST_SINGLE) {
            return [
                {
                    name: this.getHarvestPhaseName(0),
                    duration: this.data.harvest_phases[0]?.duration || null
                }
            ];
        } else {
            return [
                {
                    name: this.getHarvestPhaseName(0),
                    duration: this.data.harvest_phases[0]?.duration || null
                },
                {
                    name: this.getHarvestPhaseName(1),
                    duration: this.data.harvest_phases[1]?.duration || null
                }
            ];
        }
    },

    // Handle harvest type change
    handleHarvestTypeChange(harvestType) {
        const intType = parseInt(harvestType);

        if (intType === HARVEST_SINGLE) {
            // Single harvest: only need first harvest phase
            this.data.harvest_phases = this.initializeHarvestPhases(HARVEST_SINGLE);

            // Clear next growth phases
            this.data.growth_phases.next = {
                phases: [],
                count: 1
            };
        } else if (intType === HARVEST_MULTIPLE) {
            // Multiple harvest: need both first and next phases
            this.data.harvest_phases = this.initializeHarvestPhases(HARVEST_MULTIPLE);

            // Ensure next growth phases exist with default name
            if (this.data.growth_phases.next.phases.length === 0) {
                this.data.growth_phases.next.phases = [
                    { name: this.getGrowthPhaseDefaultName('next', 0), duration: null }
                ];
            }
        }
    },

    // Add new phase to growth phases
    addPhase(group) {
        if (group === 'first') {
            const newIndex = this.data.growth_phases.first.length;
            this.data.growth_phases.first.push({
                name: this.getGrowthPhaseDefaultName('first', newIndex),
                duration: null
            });
        } else if (group === 'next') {
            if (this.data.growth_phases.next.phases.length >= MAX_NEXT_PHASE_ITEMS) {
                return; // Cannot add more phases
            }
            const newIndex = this.data.growth_phases.next.phases.length;
            this.data.growth_phases.next.phases.push({
                name: this.getGrowthPhaseDefaultName('next', newIndex),
                duration: null
            });
        }
    },

    // Remove phase from growth phases
    removePhase(group, index) {
        if (group === 'first') {
            // Don't allow removing if only one phase remains
            if (this.data.growth_phases.first.length > 1) {
                this.data.growth_phases.first.splice(index, 1);
            }
        } else if (group === 'next') {
            // Don't allow removing if only one phase remains
            if (this.data.growth_phases.next.phases.length > 1) {
                this.data.growth_phases.next.phases.splice(index, 1);
            }
        }
    },

    canRemovePhase(group) {
        if (group === 'first') {
            return this.data.growth_phases.first.length > 1;
        } else if (group === 'next') {
            return this.data.growth_phases.next.phases.length > 1;
        }
        return false;
    },

    // Check if can add more phases
    canAddPhase(group) {
        if (group === 'first') {
            return true; // No limit for first phases
        } else if (group === 'next') {
            return this.data.growth_phases.next.phases.length < MAX_NEXT_PHASE_ITEMS;
        }
        return false;
    },

    // Auto-generate growth phases based on harvest duration
    autoGenerateGrowthPhases(harvestDuration) {
        if (harvestDuration > MAX_HARVEST_DURATION) return;

        let phases = [];

        if (harvestDuration <= MAX_PHASE_DURATION_FIRST) {
            // Case: <= 14 days - split in half, first phase gets extra day if odd
            const phase1Duration = Math.ceil(harvestDuration / 2);
            const phase2Duration = harvestDuration - phase1Duration;

            phases = [
                { name: this.getGrowthPhaseDefaultName('first', 0), duration: phase1Duration },
                { name: this.getGrowthPhaseDefaultName('first', 1), duration: phase2Duration }
            ];
        } else {
            // Case: > 14 days - split by MAX_PHASE_DURATION_FIRST cycles + remainder
            const fullCycles = Math.floor(harvestDuration / MAX_PHASE_DURATION_FIRST);
            const remainder = harvestDuration % MAX_PHASE_DURATION_FIRST;

            // Add full MAX_PHASE_DURATION_FIRST cycles
            for (let i = 0; i < fullCycles; i++) {
                phases.push({
                    name: this.getGrowthPhaseDefaultName('first', i),
                    duration: MAX_PHASE_DURATION_FIRST
                });
            }

            // Add remainder as final phase if exists
            if (remainder > 0) {
                phases.push({
                    name: this.getGrowthPhaseDefaultName('first', fullCycles),
                    duration: remainder
                });
            }
        }

        // Update form data
        this.data.growth_phases.first = phases;
    },

    // Submit form with AJAX
    submit() {
        this.isLoading = true;
        this.errors = {}; // Clear previous errors

        // Get form data
        const formData = new FormData(this.$refs.form);

        // Get form action URL
        const formAction = this.$refs.form.getAttribute('action');

        // Always use POST for AJAX, Laravel will handle _method field in FormData
        const ajaxMethod = 'POST';

        $.ajax({
            url: formAction,
            type: ajaxMethod,
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: (res) => {
                console.log('✅ Success:', res);
                this.isLoading = false;

                // Redirect to index or show success message
                if (res.redirect_url) {
                    window.location.href = res.redirect_url;
                } else {
                    // Fallback redirect
                    window.location.href = '/crops';
                }
            },
            error: (xhr) => {
                this.isLoading = false;
                const { responseJSON, status } = xhr;

                if (status === 422) {
                    // Validation errors
                    this.errors = responseJSON.errors || {};
                    console.log('❌ Validation errors:', this.errors);

                    // Scroll to first error
                    this.$nextTick(() => {
                        const firstError = document.querySelector('.text-danger');
                        if (firstError) {
                            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    });
                } else {
                    // Other errors
                    console.error('❌ Server error:', responseJSON);
                    alert('Có lỗi xảy ra. Vui lòng thử lại!');
                }
            }
        });
    },

    // Initialize Select2 integration
    initializeSelect2() {
        if (typeof $ === 'undefined') return;

        this.$nextTick(() => {
            // Setup crop_group_id select
            const cropGroupSelect = $('select[x-model="data.crop_group_id"]');
            if (cropGroupSelect.length) {
                cropGroupSelect.select2();

                // Sync Select2 changes back to Alpine
                cropGroupSelect.on('change', (e) => {
                    this.data.crop_group_id = e.target.value;
                });

                // Watch Alpine changes and update Select2
                this.$watch('data.crop_group_id', (value) => {
                    cropGroupSelect.val(value).trigger('change.select2');
                });
            }

            // Setup harvest_type select
            const harvestTypeSelect = $('select[x-model="data.harvest_type"]');
            if (harvestTypeSelect.length) {
                harvestTypeSelect.select2();

                // Sync Select2 changes back to Alpine
                harvestTypeSelect.on('change', (e) => {
                    this.data.harvest_type = parseInt(e.target.value);
                });

                // Watch Alpine changes and update Select2
                this.$watch('data.harvest_type', (value) => {
                    harvestTypeSelect.val(value).trigger('change.select2');
                });
            }
        });
    }
}));

Alpine.start();
