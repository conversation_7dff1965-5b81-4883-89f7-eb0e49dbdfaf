<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Modal extends Component
{
    public $header;
    public $size;
    public $center;

    /**
     * Create a new component instance.
     */
    public function __construct(
        $header = null,
        $size = 'md',
        $center = false
    ) {
        $this->header = $header;
        $this->size = $size;
        $this->center = filter_var($center, FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('partials.modal');
    }
}
