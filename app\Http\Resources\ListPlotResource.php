<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ListPlotResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /**
             * Plot Id.
             * @var int
             * @example 12
             */
            'id' => $this->id,
            /**
             * Plot Position.
             * @var int
             * @example 12
             */
            'x' => $this->x,
            /**
             * Plot Position.
             * @var int
             * @example 20
             */
            'y' => $this->y,
            /**
             * Plot Stage (0: not_plowed, 1: plowed, 2: planted).
             * @var 0|1|2
             */
            'stage' => $this->stage,
            /**
             * Land assignment day.
             * @var string
             * @example 2025-09-22
             */
            'start_at' => $this->start_at,
            /**
             * Expiration date.
             * @var string
             * @example 2026-09-22
             */
            'end_at' => $this->end_at,
            'plant' => new PlantResource($this->plant),
        ];
    }
}
