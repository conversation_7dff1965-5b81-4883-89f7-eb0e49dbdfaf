<?php

namespace App\Logics;
use App\Enums\OrderStatus;
use App\Enums\OrderType;
use App\Helpers\DateTimeHelper;
use App\Helpers\StringHelper;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class OrderManager
{
    protected $dateTimeHelper;
    protected $stringHelper;

    public function __construct(DateTimeHelper $dateTimeHelper, StringHelper $stringHelper)
    {
        $this->dateTimeHelper = $dateTimeHelper;
        $this->stringHelper = $stringHelper;
    }

    /**
     * Get all orders with filters
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getOrderList(Request $request)
    {
        // Build query for orders with user and latest payment relationship
        $orders = Order::select(
            'orders.id',
            'orders.code',
            'orders.name',
            'orders.status',
            'orders.type',
            'orders.amount',
            'orders.currency',
            'orders.created_at',
            'orders.paid_at',
            'orders.user_id',
            'orders.latest_payment_id'
        )
            ->with([
                'user:id,name,avatar',
                'latestPayment:id,channel'
            ])
            ->orderBy('orders.created_at', 'desc');

        // Apply filters - using isset() pattern like getUserList
        if (isset($request->id)) {
            $items = array_filter(array_map("trim", explode(",", $request->id)));
            if (!empty($items)) {
                $orders->whereIn('orders.id', $items);
            }
        }

        if (isset($request->code)) {
            $code = $this->stringHelper->formatStringWhereLike($request->code);
            $orders->where('orders.code', 'LIKE', '%' . $code . '%');
        }

        if (isset($request->user)) {
            $orders->where('orders.user_id', $request->user);
        }

        if (isset($request->name)) {
            $name = $this->stringHelper->formatStringWhereLike($request->name);
            $orders->where('orders.name', 'LIKE', '%' . $name . '%');
        }

        if (isset($request->status)) {
            $statusArray = is_array($request->status) ? $request->status : [$request->status];
            $orders->whereIn('orders.status', $statusArray);
        }

        if (isset($request->type)) {
            $typeArray = is_array($request->type) ? $request->type : [$request->type];
            $orders->whereIn('orders.type', $typeArray);
        }

        if (isset($request->channel)) {
            $channelArray = is_array($request->channel) ? $request->channel : [$request->channel];
            $orders->whereExists(function ($query) use ($channelArray) {
                $query->select(DB::raw(1))
                      ->from('payments')
                      ->whereColumn('payments.id', 'orders.latest_payment_id')
                      ->whereIn('payments.channel', $channelArray);
            });
        }

        if (isset($request->from)) {
            $orders->whereDate('orders.created_at', '>=', Carbon::parse($request->from));
        }

        if (isset($request->to)) {
            $orders->whereDate('orders.created_at', '<=', Carbon::parse($request->to));
        }

        if (isset($request->from_completed)) {
            $orders->whereDate('orders.paid_at', '>=', Carbon::parse($request->from_completed));
        }

        if (isset($request->to_completed)) {
            $orders->whereDate('orders.paid_at', '<=', Carbon::parse($request->to_completed));
        }

        return $orders;
    }

    /**
     * Create a new order.
     */
    public function create(array $data): Order
    {
        return Order::create($data);
    }

    /**
     * Update an existing order.
     */
    public function update(Order $order, array $data): Order
    {
        $order->update($data);
        return $order;
    }

    /**
     * Delete an order.
     */
    public function delete(Order $order): void
    {
        $order->delete();
    }
}
