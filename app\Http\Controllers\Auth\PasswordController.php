<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\ChangePasswordRequest;
use App\Logics\UserManager;

class PasswordController extends Controller
{

    public function __construct(private UserManager $userManager) {}
    /**
     * Update the user password.
     * @response array{"data": null, message: "Thay đổi mật khẩu thành công."}
     */
    public function update(ChangePasswordRequest $request)
    {
        $newPassword = $request->validated('password');
        $this->userManager->updatePassword($request->user(), $newPassword);
        return response()->json([
            'data' => null,
            'message' => __('message.password_update_successfully')
        ]);
    }
}
