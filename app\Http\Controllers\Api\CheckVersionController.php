<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\CheckVersionRequest;
use App\Logics\VersionManager;
use Exception;
use Illuminate\Support\Facades\Log;

class CheckVersionController extends Controller
{
    protected $versionManager;

    /**
     * Constructor
     *
     * @param VersionManager $versionManager
     */
    public function __construct(VersionManager $versionManager)
    {
        $this->versionManager = $versionManager;
    }

    /**
     * Check version for API response
     *
     * @param CheckVersionRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @response array{"message": "Success", "data": App\Http\Resources\CheckVersionResource}
     * @throws Exception
     */
    public function checkVersion(CheckVersionRequest $request): \Illuminate\Http\JsonResponse
    {
        $platform = $request->device;
        $currentVersion = $request->version;

        // Get version data from service
        $versionData = $this->versionManager->checkVersion($platform, $currentVersion);

        // return success response with version data
        return response()->json([
            'message' => __('message.version_check_success'),
            'data' => $versionData
        ]);
    }
}
