<?php

namespace Database\Seeders;

use App\Models\Land;
use App\Models\Plot;
use Illuminate\Database\Seeder;

class LandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $lands = [
            [
                'name' => 'Khu A',
                'width' => 20,
                'height' => 15,
            ],
            [
                'name' => 'Khu B',
                'width' => 30,
                'height' => 25,
            ],
        ];
        foreach ($lands as $land) {
            $land = Land::create($land);
            $bulk = [];
            for ($y = 1; $y <= $land->height; $y++) {
                for ($x = 1; $x <= $land->width; $x++) {
                    $bulk[] = [
                        'land_id' => $land->id,
                        'x' => $x,
                        'y' => $y,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
            Plot::insert($bulk);
        }
    }
}
