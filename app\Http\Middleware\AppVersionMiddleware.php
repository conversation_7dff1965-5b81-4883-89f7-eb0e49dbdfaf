<?php

namespace App\Http\Middleware;

use App\Logics\VersionManager;
use \Illuminate\Http\Response as Res;
use App\Http\Requests\CheckVersionRequest;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;

class AppVersionMiddleware
{
    protected $versionManager;

    /**
     * Route names to skip version checking.
     * Make sure these routes are named in api.php.
     */
    protected array $except = [
        '*.check.version'
    ];

    public function __construct(VersionManager $versionManager)
    {
        $this->versionManager = $versionManager;
    }

    public function handle(Request $request, Closure $next): Response
    {
        // Skip version check for excluded routes
        if ($request->routeIs($this->except)) {
            return $next($request);
        }

        // Get device and version from headers
        $device = $request->headers->get('device');
        $version = $request->headers->get('version');

        // If no headers provided → pass through
        if (!$device || !$version) {
            return $next($request);
        }

        // If headers invalid → pass through
        $checkVersionRequest = new CheckVersionRequest();
        $validator = Validator::make([
            'device' => $device,
            'version' => $version
        ], $checkVersionRequest->rules(), $checkVersionRequest->messages());

        if ($validator->fails()) {
            return $next($request);
        }

        // Call versionManager::checkVersion() to get full information
        $versionData = $this->versionManager->checkVersion((int)$device, $version);

        // If force update required → return HTTP 426 with headers and response data
        if ($versionData['force_update']) {
            $data = [
                'mode' => $versionData['mode'],
                'force_update' => $versionData['force_update'],
                'description' => $versionData['description']
            ];

            $response = response()->json([
                'message' => __('message.version_update_required'),
                'data' => $data
            ], Res::HTTP_UPGRADE_REQUIRED);
            return $response;
        }

        // If no force update needed → pass through
        return $next($request);
    }
}
