<?php

namespace App\Enums;

enum OrderStatus: string
{
    case PENDING   = 'pending';     // Created, awaiting payment
    case PROCESSING= 'processing';  // Payment in-progress (optional)
    case PAID      = 'paid';        // Fully paid
    case FAILED    = 'failed';      // Failed attempt
    case EXPIRED   = 'expired';     // Expired and closed
    case CANCELED  = 'canceled';    // Canceled by user/system

    /**
     * Get the label for the order status
     */
    public function label(): string
    {
        return match($this) {
            self::PENDING => __('language.pending'),
            self::PROCESSING => __('language.processing'),
            self::PAID => __('language.paid'),
            self::FAILED => __('language.failure'),
            self::EXPIRED => __('language.expired'),
            self::CANCELED => __('language.canceled'),
        };
    }
}
