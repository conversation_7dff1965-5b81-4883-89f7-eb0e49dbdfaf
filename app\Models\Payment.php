<?php

namespace App\Models;

use App\Enums\PaymentChannel;
use App\Enums\PaymentStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasUuids;

    protected $fillable = [
        'order_id','channel','status','amount','currency','attempt_no',
        'provider_transaction_id','expires_at','paid_at','meta'
    ];

    protected $casts = [
        'channel'   => PaymentChannel::class,
        'status'    => PaymentStatus::class,
        'meta'      => 'array',
        'expires_at'=> 'datetime',
        'paid_at'   => 'datetime',
    ];

    public function order() {
        return $this->belongsTo(Order::class);
    }

    public function transactions() {
        return $this->hasMany(PaymentTransaction::class);
    }

    public function logs() {
        return $this->hasMany(PaymentLog::class);
    }

    protected static function booted()
    {
        // Update order's latest_payment_id when a new payment is created
        static::created(function ($payment) {
            $payment->order->updateLatestPaymentId();
        });
    }
}
