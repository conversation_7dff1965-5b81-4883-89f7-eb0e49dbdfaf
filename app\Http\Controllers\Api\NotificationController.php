<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Logics\NotificationManager;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class NotificationController extends Controller
{
    protected NotificationManager $notificationManager;

    public function __construct(NotificationManager $notificationManager)
    {
        $this->notificationManager = $notificationManager;
    }

    /**
     * API 5: Send push notification
     */
    //TODO: remove after tested
    public function send(Request $request)
    {
        $userIds = $request->input('user_ids');
        $notificationTypeId = $request->input('notification_type_id');
        $title = $request->input('title');
        $body = $request->input('body');
        $data = $request->input('data');

        $notification = $this->notificationManager->sendToUsers(
            $userIds,
            $notificationTypeId,
            $title,
            $body,
            $data
        );

        return response()->json([
            'message' => trans('message.success'),
            'data' => $notification
        ], Response::HTTP_OK);
    }
    /**
     * API 6: List notifications by user
     */
    public function list(Request $request, int $userId)
    {
        $notifications = $this->notificationManager->getUserNotifications($userId, $request);

        return response()->json([
            'message' => trans('message.success'),
            'data' => $notifications
        ], Response::HTTP_OK);
    }

    /**
     * API 7: Mark all as read
     */
    public function readNotifications(Request $request, int $userId)
    {
        $updatedCount = $this->notificationManager->markAsRead($userId, $request);
        if ($updatedCount > 0) {
            return response()->json([
                'message' => trans('message.success'),
                'data' => $updatedCount
            ], Response::HTTP_OK);
        }
    }
}
