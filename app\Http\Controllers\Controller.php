<?php

namespace App\Http\Controllers;

use App\Traits\RedirectInvalidPage;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator;

abstract class Controller
{
    use RedirectInvalidPage;

    protected function apiJson($data = null, $message = null, $status = Response::HTTP_OK, $extra = [])
    {
        $defaultMessage = $status === Response::HTTP_OK ? __('message.success') : __('message.failure');
        return response()->json([
            'message' => $message ?? $defaultMessage,
            'data' => $data,
            ...$extra,
        ], $status);
    }

    protected function apiJsonPagination(LengthAwarePaginator $pagination, $extra = [])
    {
        $data = [
            'message' => __('message.success'),
            ...$extra,
            'current_page' => $pagination->currentPage(),
            'total_page' => $pagination->lastPage(),
            'per_page' => $pagination->perPage(),
            'total' => $pagination->total(),
            'data' => $pagination->items(),
        ];

        return $this->apiJson(extra: $data, status: Response::HTTP_OK);
    }
}
