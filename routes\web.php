<?php

use App\Http\Controllers\AddressController;
use App\Http\Controllers\CropController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\AvatarShowController;
use App\Http\Controllers\LandController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\Version\VersionController;
use App\Models\Role;
use Illuminate\Support\Facades\Route;
use UniSharp\LaravelFilemanager\Lfm;

Route::get('/', function () {
    return redirect()->route('user.index');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // User avatar
    Route::get('/avatar/{id}', [UserController::class, 'getAvatar'])->name('user.avatar');

    // AJAX routes
    Route::get('getListUser', [UserController::class, 'getListUser'])->name('getListUser');

    // Order management routes - Admin only
    Route::middleware(['session', 'role:' . implode('|', [Role::ROLE_SYSTEM_MANAGER])])->group(function () {
        Route::get('orders', [OrderController::class, 'index'])->name('orders.index');
    });

    // Get administrative units
    Route::get('getCommuneList', [AddressController::class, 'getCommuneList'])->name('getCommuneList');

    // Payment
    Route::post('/payments/start', [PaymentController::class, 'start'])->name('payments.start');

    // User management routes
    Route::group([
        'middleware' => ['role:' . implode('|', [Role::ROLE_SYSTEM_MANAGER, Role:: ROLE_OPERATION_MANAGER])]
    ], function () {
        Route::resource('user', UserController::class);
    });

    // Version
    Route::group([
        'prefix' => 'versions',
        'as' => 'versions.',
        'middleware' => ['role:' . implode('|', [Role::ROLE_SYSTEM_MANAGER])]
    ], function () {
        Route::get('/', [VersionController::class, 'index'])->name('index');
        Route::get('/create', [VersionController::class, 'create'])->name('create');
        Route::post('/store', [VersionController::class, 'store'])->name('store');
        Route::get('/edit/{id}', [VersionController::class, 'edit'])->name('edit');
        Route::put('/update/{id}', [VersionController::class, 'update'])->name('update');
    });

    // Files manager
    Route::group([
        'prefix' => config('lfm.route_prefix', 'files-manager'),
        'middleware' => ['role:' . implode('|', [Role::ROLE_SYSTEM_MANAGER])],
    ], function () {
        Lfm::routes();
        Route::get('/games', function () {
            return view('files-manager.games.index');
        })->name('files-manager.games.index');
    });

    // Land management
    Route::group([
        'prefix' => 'lands', 'as' => 'lands.',
        'middleware' => ['role:' . implode('|', [Role::ROLE_SYSTEM_MANAGER])]
    ], function () {
        Route::get('{land}/grid', [LandController::class, 'grid'])->name('grid');
        Route::post('lands/plots/assign', [LandController::class, 'assignPlots'])->name('plots.assign');
    });

    // Crops management
    Route::group([
        'prefix' => 'crops', 'as' => 'crops.',
        'middleware' => ['role:' . implode('|', [Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER])]
    ], function () {
        Route::get('/', [CropController::class, 'index'])->name('index');
        Route::get('/create', [CropController::class, 'create'])->name('create');
        Route::post('/store', [CropController::class, 'store'])->name('store');
        Route::get('/edit/{id}', [CropController::class, 'edit'])->name('edit');
        Route::put('/update/{id}', [CropController::class, 'update'])->name('update');
        Route::delete('/destroy/{id}', [CropController::class, 'destroy'])->name('destroy');
    });
});

// Payment
Route::get('/payments/return/{channel}', [PaymentController::class, 'return'])->name('payments.return');
Route::match(['GET','POST'], '/payments/ipn/{channel}', [PaymentController::class, 'ipn'])->name('payments.ipn');
Route::get('/payments/result/{payment}', [PaymentController::class, 'result'])->name('payments.result');

require __DIR__.'/auth.php';
