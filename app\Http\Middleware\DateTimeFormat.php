<?php

namespace App\Http\Middleware;

use App\Helpers\DateTimeHelper;
use App\Models\Language;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class DateTimeFormat
{
    const DATETIME_FIELDS = [
        'birthday',
        'from',
        'to',
        'from_completed',
        'to_completed',
        'start_at',
        'end_at'
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $language = App::getLocale();

        if ($language == Language::EN) {
            return $next($request);
        }

        foreach (self::DATETIME_FIELDS as $field) {
            if (!$request->has($field)) {
                continue;
            }
            // Convert date format by language
            $request->merge([
                $field => DateTimeHelper::convertToLanguage($request->input($field), $language, Language::EN)
            ]);
        }

        return $next($request);
    }
}
