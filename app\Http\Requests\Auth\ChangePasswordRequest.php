<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class ChangePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            /**
             * Old password.
             * @example Abcd!23
             */
            'old_password' => ['required', 'current_password'],
            /**
             * New password.
             * @example Abcd!23
             */
            'password' => [
                'required',
                'confirmed',
                Password::defaults(),
            ],
        ];
    }
}
