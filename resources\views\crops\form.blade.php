@extends('layouts.master')
@section('title', __('language.crops_management'))

@section('css_library')
    @include('partials.style-library', ['select2' => true, 'icheck' => true])
@endsection

@section('css_page')
    @vite('resources/build/js/pages/crop-form.js')
@endsection

@section('js_library')
    <script src="{{ asset('plugins/jquery-validation/additional-methods.min.js') }}"></script>
    @include('partials.script-library', ['select2' => true])
@endsection

@section('header')
    <li class="nav-item">
        {{ __('language.crops_management') }}
    </li>
@endsection

@section('content')
<section class="content pt-3">
    @include('partials.breadcrumb', [
        'item' => '<a href="' . route('crops.index') . '">' . __('language.crops_management') . '</a>
        &nbsp;/&nbsp;' . $headerTitle
    ])
    <form id="form-crop"
          action="{{ $route }}"
          method="POST"
          x-ref="form"
          x-data='cropForm(@json(old()), @json($errors->getMessages()), @json($cropDataForJs), @json($availableLanguages))'>
        @csrf
        @if ($isEdit)
            @method('PUT')
        @endif

            <div class="row ml-0 mx-0">
                <!-- Main Content Area -->
                <div class="col-xl-10">
                    <div class="card">
                        <div class="card-header">
                            <h3>{{ $headerTitle }}</h3>
                        </div>
                        <div class="card-body">
                            <!-- Multilingual Name & Description -->
                            {{-- Vietnamese (Required) --}}
                            <div class="form-group">
                                <label>{{ __('language.crop_name') }} <span class="text-danger">*</span></label>
                                <input type="text"
                                       name="vi[name]"
                                       x-model="data.vi.name"
                                       class="form-control"
                                       placeholder="{{ __('language.crop_name_placeholder') }}">
                                <template x-if="errors['vi.name']">
                                    <span class="text-danger" x-text="errors['vi.name']"></span>
                                </template>
                            </div>

                            <div class="form-group">
                                <label>{{ __('language.description') }}</label>
                                <textarea name="vi[description]"
                                          x-model="data.vi.description"
                                          rows="5"
                                          class="form-control"
                                          placeholder="{{ __('language.description_placeholder') }}"></textarea>
                                <template x-if="errors['vi.description']">
                                    <span class="text-danger" x-text="errors['vi.description']"></span>
                                </template>
                            </div>

                            {{-- Other languages (only if active) --}}
                            @foreach ($availableLanguages as $language)
                                @if ($language->code !== $const['vi'])
                                    <hr>
                                    <div class="language-section mb-4">
                                        <h6>{{ $language->display_name }}</h6>
                                        <div class="form-group">
                                            <label>{{ __('language.crop_name') }}</label>
                                            <input type="text"
                                                   :name="`{{ $language->code }}[name]`"
                                                   x-model="data.{{ $language->code }}.name"
                                                   class="form-control">
                                            <template x-if="errors['{{ $language->code }}.name']">
                                                <span class="text-danger" x-text="errors['{{ $language->code }}.name']"></span>
                                            </template>
                                        </div>
                                        <div class="form-group">
                                            <label>{{ __('language.description') }}</label>
                                            <textarea :name="`{{ $language->code }}[description]`"
                                                      x-model="data.{{ $language->code }}.description"
                                                      rows="5"
                                                      class="form-control"></textarea>
                                            <template x-if="errors['{{ $language->code }}.description']">
                                                <span class="text-danger" x-text="errors['{{ $language->code }}.description']"></span>
                                            </template>
                                        </div>
                                    </div>
                                @endif
                            @endforeach

                            <!-- Crop Group & Cost -->
                            <div class="row">
                                {{-- Crop group --}}
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('language.crop_group') }} <span class="text-danger">*</span></label>
                                        <select x-model="data.crop_group_id"
                                                name="crop_group_id"
                                                class="select2-base"
                                                style="width: 100%">
                                            <option value="">{{ __('language.select_crop_group') }}</option>
                                            @foreach ($cropGroupOptions as $id => $name)
                                                <option value="{{ $id }}">{{ $name }}</option>
                                            @endforeach
                                        </select>
                                        <template x-if="errors.crop_group_id">
                                            <span class="text-danger" x-text="errors.crop_group_id"></span>
                                        </template>
                                    </div>
                                </div>
                                {{-- Cost gold --}}
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>{{ __('language.cost') }} <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number"
                                                   name="cost[gold]"
                                                   x-model="data.cost.gold"
                                                   class="form-control"
                                                   min="1"
                                                   placeholder="{{ __('language.cost_placeholder') }}">
                                            <div class="input-group-append">
                                                <span class="input-group-text">{{ __('language.gold') }}</span>
                                            </div>
                                        </div>
                                        <template x-if="errors['cost.gold']">
                                            <span class="text-danger" x-text="errors['cost.gold']"></span>
                                        </template>
                                    </div>
                                </div>
                                {{-- Cost gem - COMMENTED OUT --}}
                                {{-- <div class="col-md-4">
                                    <div class="form-group">
                                        <label>{{ __('language.cost') }} (Gem)</label>
                                        <div class="input-group">
                                            <input type="number" name="cost[gem]" x-model="data.cost.gem" class="form-control" min="0">
                                            <div class="input-group-append">
                                                <span class="input-group-text">{{ __('language.gem') }}</span>
                                            </div>
                                        </div>
                                        <template x-if="errors['cost.gem']">
                                            <span class="text-danger" x-text="errors['cost.gem']"></span>
                                        </template>
                                    </div>
                                </div> --}}
                            </div>

                            <hr>

                            <!-- Harvest Type -->
                            <div class="form-group">
                                <label>{{ __('language.harvest_type') }} <span class="text-danger">*</span></label>
                                <select x-model="data.harvest_type"
                                        name="harvest_type"
                                        class="select2-base"
                                        style="width: 100%">
                                    <option value="{{ $const['single'] }}">{{ __('language.single_harvest') }}</option>
                                    <option value="{{ $const['multiple'] }}">{{ __('language.multiple_harvest') }}</option>
                                </select>
                                <template x-if="errors.harvest_type">
                                    <span class="text-danger" x-text="errors.harvest_type"></span>
                                </template>
                            </div>

                            <!-- Harvest Phases -->
                            <div class="harvest-phases-container">
                                <h6>{{ __('language.estimated_harvest_time') }}</h6>

                                <!-- Dynamic Harvest Phases -->
                                <template x-for="(phase, index) in data.harvest_phases" :key="index">
                                    <div class="harvest-phase-item">
                                        <label x-text="index === 0 ? '{{ __('language.first_harvest') }}' : '{{ __('language.next_harvest') }}'"></label>
                                        <div class="form-group">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <input type="text"
                                                           :name="`harvest_phases[${index}][name]`"
                                                           x-model="data.harvest_phases[index].name"
                                                           class="form-control"
                                                           :placeholder="index === 0 ? '{{ __('language.first_harvest_placeholder') }}' : '{{ __('language.next_harvest_placeholder') }}'">
                                                    <template x-if="errors[`harvest_phases.${index}.name`]">
                                                        <span class="text-danger" x-text="errors[`harvest_phases.${index}.name`]"></span>
                                                    </template>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="input-group">
                                                        <input type="number"
                                                               :name="`harvest_phases[${index}][duration]`"
                                                               x-model="data.harvest_phases[index].duration"
                                                               class="form-control harvest-duration"
                                                               min="1" max="200"
                                                               placeholder="{{ __('language.duration_placeholder') }}">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text">{{ __('language.days') }}</span>
                                                        </div>
                                                    </div>
                                                    <template x-if="errors[`harvest_phases.${index}.duration`]">
                                                        <span class="text-danger" x-text="errors[`harvest_phases.${index}.duration`]"></span>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>

                            <hr>

                            <!-- First Growth Phases -->
                            <div class="growth-phase-group">
                                <h6>{{ __('language.first_growth_phases') }}</h6>
                                <div class="growth-phases-container">
                                    <template x-for="(phase, index) in data.growth_phases.first" :key="index">
                                        <div class="form-group phase-item">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <input type="text"
                                                           :name="`growth_phases[first][${index}][name]`"
                                                           x-model="data.growth_phases.first[index].name"
                                                           class="form-control"
                                                           :placeholder="`{{ __('language.phase_name') }} ${index + 1}`">
                                                    <template x-if="errors[`growth_phases.first.${index}.name`]">
                                                        <span class="text-danger" x-text="errors[`growth_phases.first.${index}.name`]"></span>
                                                    </template>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="input-group">
                                                        <input type="number"
                                                               :name="`growth_phases[first][${index}][duration]`"
                                                               x-model="data.growth_phases.first[index].duration"
                                                               class="form-control"
                                                               min="1" max="{{ $const['max_phase_duration_first'] }}"
                                                               placeholder="{{ __('language.duration_placeholder') }}">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text">{{ __('language.days') }}</span>
                                                        </div>
                                                        <button type="button"
                                                                class="btn btn-sm btn-danger"
                                                                @click="removePhase('first', index)"
                                                                x-show="canRemovePhase('first')"
                                                                style="height: auto">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                    <template x-if="errors[`growth_phases.first.${index}.duration`]">
                                                        <span class="text-danger" x-text="errors[`growth_phases.first.${index}.duration`]"></span>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                                <button type="button"
                                        class="btn btn-sm btn-outline-primary"
                                        @click="addPhase('first')">
                                    <i class="fas fa-plus"></i> {{ __('language.add_phase') }}
                                </button>
                            </div>

                            <!-- Next Growth Phases -->
                            <div class="growth-phase-group second-phases mt-3 mb-3"
                                x-show="parseInt(data.harvest_type) === {{ $const['multiple'] }}">
                                <h6>{{ __('language.next_growth_phases') }}</h6>
                                <div class="form-group mt-2">
                                    <label>{{ __('language.repeat_count') }}</label>
                                    <input type="number"
                                           name="growth_phases[next][count]"
                                           x-model="data.growth_phases.next.count"
                                           class="form-control"
                                           min="1" max = "{{ $const['max_phase_count_next'] }}"
                                           placeholder="{{ __('language.repeat_count_placeholder') }}">
                                    <template x-if="errors['growth_phases.next.count']">
                                        <span class="text-danger" x-text="errors['growth_phases.next.count']"></span>
                                    </template>
                                </div>
                                <div class="growth-phases-container">
                                    <template x-for="(phase, index) in data.growth_phases.next.phases" :key="index">
                                        <div class="form-group phase-item">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <input type="text"
                                                           :name="`growth_phases[next][phases][${index}][name]`"
                                                           x-model="data.growth_phases.next.phases[index].name"
                                                           class="form-control"
                                                           :placeholder="`{{ __('language.phase_name') }} ${index + 1}`">
                                                    <template x-if="errors[`growth_phases.next.phases.${index}.name`]">
                                                        <span class="text-danger" x-text="errors[`growth_phases.next.phases.${index}.name`]"></span>
                                                    </template>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="input-group">
                                                        <input type="number"
                                                               :name="`growth_phases[next][phases][${index}][duration]`"
                                                               x-model="data.growth_phases.next.phases[index].duration"
                                                               class="form-control"
                                                               min="1" max="{{ $const['max_phase_duration_next'] }}"
                                                               placeholder="{{ __('language.duration_placeholder') }}">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text">{{ __('language.days') }}</span>
                                                        </div>
                                                        <button type="button"
                                                                class="btn btn-sm btn-danger"
                                                                @click="removePhase('next', index)"
                                                                x-show="canRemovePhase('next')"
                                                                style="height: auto">
                                                            <i class="fas fa-minus"></i>
                                                        </button>
                                                    </div>
                                                    <template x-if="errors[`growth_phases.next.phases.${index}.duration`]">
                                                        <span class="text-danger" x-text="errors[`growth_phases.next.phases.${index}.duration`]"></span>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                                <button type="button"
                                        class="btn btn-sm btn-outline-primary"
                                        @click="addPhase('next')"
                                        x-show="canAddPhase('next')">
                                    <i class="fas fa-plus"></i> {{ __('language.add_phase') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Button -->
                <div class="col-xl-2">
                    <div class="action-sticky">
                        <button type="button"
                                class="btn btn-primary"
                                @click="submit()"
                                :disabled="isLoading">
                            <span x-show="!isLoading">
                                {{ __('language.save') }}
                            </span>
                            <span x-show="isLoading">
                                <i class="fas fa-spinner fa-spin"></i> {{ __('language.saving') }}
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </form>

</section>
@endsection