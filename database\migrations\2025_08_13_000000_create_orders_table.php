<?php

use App\Enums\OrderStatus;
use App\Enums\OrderType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->unsignedBigInteger('user_id');
            $table->index('user_id');

            // Business payload per order type
            $table->string('type')->default(OrderType::ORDER_PAYMENT->value);
            $table->string('code')->unique(); // human-readable order code/slug
            $table->unsignedBigInteger('amount'); // smallest unit (VND)
            $table->string('currency', 3)->default('VND');
            $table->string('status')->default(OrderStatus::PENDING->value);
            $table->json('meta')->nullable(); // flexible payload per type

            // For quick queries
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'type']);
            $table->index(['status']);
        });
    }

    public function down(): void {
        Schema::dropIfExists('orders');
    }
};
