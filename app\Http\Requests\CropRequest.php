<?php

namespace App\Http\Requests;

use App\Models\Crop;
use App\Models\Language;
use Illuminate\Foundation\Http\FormRequest;

class CropRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $activeLanguages = Language::active()->pluck('code')->toArray();
        $rules = [];

        // Multilingual rules
        foreach ($activeLanguages as $langCode) {
            $rules[$langCode . '.name'] = ['required', 'string', 'max:255'];
            $rules[$langCode . '.description'] = ['nullable', 'string', 'max:' . Crop::MAX_DESCRIPTION_LENGTH];
        }

        return array_merge($rules, [
            'crop_group_id' => ['required', 'integer', 'exists:crop_groups,id'],
            'cost.gold' => ['required', 'numeric', 'min:1'],
            'cost.gem' => ['nullable', 'numeric', 'min:0'],
            'harvest_type' => ['required', 'in:' . implode(',', [Crop::SINGLE, Crop::MULTIPLE])],
            'harvest_phases' => ['required', 'array', 'min:1'],
            'harvest_phases.*.name' => ['required', 'string', 'max:255'],
            'harvest_phases.*.duration' => [
                'required',
                'integer',
                'min:' . Crop::MIN_HARVEST_DURATION,
                'max:' . Crop::MAX_HARVEST_DURATION
            ],
            'growth_phases' => ['required', 'array'], // Object {first: ..., next: ...}
            'growth_phases.first' => ['required', 'array', 'min:1'],
            'growth_phases.first.*.name' => ['required', 'string', 'max:255'],
            'growth_phases.first.*.duration' => [
                'required',
                'integer',
                'min:' . Crop::MIN_PHASE_DURATION,
                'max:' . Crop::MAX_PHASE_DURATION_FIRST
            ],
            'growth_phases.next' => ['required_if:harvest_type,' . Crop::MULTIPLE, 'array'],
            'growth_phases.next.phases' => ['required_if:harvest_type,' . Crop::MULTIPLE, 'array', 'min:1'],
            'growth_phases.next.phases.*.name' => ['required_if:harvest_type,' . Crop::MULTIPLE, 'string', 'max:255'],
            'growth_phases.next.phases.*.duration' => [
                'required_if:harvest_type,' . Crop::MULTIPLE,
                'integer',
                'min:' . Crop::MIN_PHASE_DURATION,
                'max:' . Crop::MAX_PHASE_DURATION_NEXT
            ],
            'growth_phases.next.count' => ['required_if:harvest_type,' . Crop::MULTIPLE, 'integer', 'min:1', 'max:'. Crop::MAX_PHASE_COUNT_NEXT],
        ]);
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            // Multilingual
            '*.name.required' => __('validation.required', ['attribute' => __('validation.attributes.crop_name')]),
            '*.name.max' => __('validation.max.string', ['attribute' => __('validation.attributes.crop_name'), 'max' => 255]),
            '*.description.max' => __('validation.max.string', ['attribute' => __('validation.attributes.description'), 'max' => Crop::MAX_DESCRIPTION_LENGTH]),

            // Crop group
            'crop_group_id.required' => __('validation.required', ['attribute' => __('validation.attributes.crop_group')]),
            'crop_group_id.exists' => __('validation.exists', ['attribute' => __('validation.attributes.crop_group')]),

            // Cost
            'cost.gold.required' => __('validation.required', ['attribute' => __('validation.attributes.cost')]),
            'cost.gold.min' => __('validation.min.numeric', ['attribute' => __('validation.attributes.cost'), 'min' => 1]),
            'cost.gem.min' => __('validation.min.numeric', ['attribute' => __('validation.attributes.cost'), 'min' => 0]),

            // Harvest type
            'harvest_type.required' => __('validation.required', ['attribute' => __('validation.attributes.harvest_type')]),
            'harvest_type.in' => __('validation.in', ['attribute' => __('validation.attributes.harvest_type')]),

            // Harvest phases
            'harvest_phases.required' => __('validation.required', ['attribute' => __('validation.attributes.harvest_phases')]),
            'harvest_phases.min' => __('validation.min.array', ['attribute' => __('validation.attributes.harvest_phases'), 'min' => 1]),
            'harvest_phases.*.name.required' => __('validation.required', ['attribute' => __('validation.attributes.harvest_name')]),
            'harvest_phases.*.name.max' => __('validation.max.string', ['attribute' => __('validation.attributes.harvest_name'), 'max' => 255]),
            'harvest_phases.*.duration.required' => __('validation.required', ['attribute' => __('validation.attributes.harvest_duration')]),
            'harvest_phases.*.duration.min' => __('validation.min.numeric', ['attribute' => __('validation.attributes.harvest_duration'), 'min' => Crop::MIN_HARVEST_DURATION]),
            'harvest_phases.*.duration.max' => __('validation.max.numeric', ['attribute' => __('validation.attributes.harvest_duration'), 'max' => Crop::MAX_HARVEST_DURATION]),

            // Growth phases
            'growth_phases.required' => __('validation.required', ['attribute' => __('validation.attributes.growth_phases')]),
            'growth_phases.first.required' => __('validation.required', ['attribute' => __('validation.attributes.growth_phases')]),
            'growth_phases.first.min' => __('validation.min.array', ['attribute' => __('validation.attributes.growth_phases'), 'min' => 1]),
            'growth_phases.first.*.name.required' => __('validation.required', ['attribute' => __('validation.attributes.phase_name')]),
            'growth_phases.first.*.name.max' => __('validation.max.string', ['attribute' => __('validation.attributes.phase_name'), 'max' => 255]),
            'growth_phases.first.*.duration.required' => __('validation.required', ['attribute' => __('validation.attributes.phase_duration')]),
            'growth_phases.first.*.duration.min' => __('validation.min.numeric', ['attribute' => __('validation.attributes.phase_duration'), 'min' => Crop::MIN_PHASE_DURATION]),
            'growth_phases.first.*.duration.max' => __('validation.max.numeric', ['attribute' => __('validation.attributes.phase_duration'), 'max' => Crop::MAX_PHASE_DURATION_FIRST]),
            'growth_phases.next.required_if' => __('validation.required', ['attribute' => __('validation.attributes.growth_phases')]),
            'growth_phases.next.phases.required_if' => __('validation.required', ['attribute' => __('validation.attributes.growth_phases')]),
            'growth_phases.next.phases.min' => __('validation.min.array', ['attribute' => __('validation.attributes.growth_phases'), 'min' => 1]),
            'growth_phases.next.phases.*.name.required_if' => __('validation.required', ['attribute' => __('validation.attributes.phase_name')]),
            'growth_phases.next.phases.*.name.max' => __('validation.max.string', ['attribute' => __('validation.attributes.phase_name'), 'max' => 255]),
            'growth_phases.next.phases.*.duration.required_if' => __('validation.required', ['attribute' => __('validation.attributes.phase_duration')]),
            'growth_phases.next.phases.*.duration.min' => __('validation.min.numeric', ['attribute' => __('validation.attributes.phase_duration'), 'min' => Crop::MIN_PHASE_DURATION]),
            'growth_phases.next.phases.*.duration.max' => __('validation.max.numeric', ['attribute' => __('validation.attributes.phase_duration'), 'max' => Crop::MAX_PHASE_DURATION_NEXT]),
            // Count
            'growth_phases.next.count.required_if' => __('validation.required', ['attribute' => __('validation.attributes.phase_count')]),
            'growth_phases.next.count.min' => __('validation.min.numeric', ['attribute' => __('validation.attributes.phase_count'), 'min' => 1]),
            'growth_phases.next.count.max' => __('validation.max.numeric', ['attribute' => __('validation.attributes.phase_count'), 'max' => Crop::MAX_PHASE_COUNT_NEXT]),
        ];
    }
}
