@extends('layouts.master')
@section('title', __('language.land_management'))
@section('meta')
@stop

@section('css_library')
    @include('partials.style-library', [
        'datepicker' => true,
        'select2' => true,
    ])
@stop

@section('css_page')
    @vite('resources/build/js/pages/lands-grid.js')
@endsection

@section('header')
    <li class="nav-item">
        {{ trans('language.land_management') }}
    </li>
@endsection
@php
    $rows = $plots->sortBy(['y', 'x'])->groupBy('y');
    function rowLabel($y)
    {
        return $y >= 1 && $y <= 26 ? chr(64 + $y) : (string) $y;
    }
@endphp
@section('content')
    <section class="content plan-grid-page">
        @include('partials.breadcrumb', [
            'item' => '<a href="' . route('versions.index') . '">' . __('language.land_management') . '</a>',
        ])
        <div class="row">
            <div class="col-xl-10">
                <div class="card">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <h3 class="card-title mb-0">
                                {{ __('language.area', ['name' => $land->name]) }}
                            </h3>
                            <span class="ml-3 text-muted small">
                                {!! __('language.land_size', ['x' => $land->width, 'y' => $land->height]) !!}
                            </span>
                        </div>
                        <div class="card-tools d-flex align-items-center">
                            <span class="mr-2">
                                <span class="badge badge-success">&nbsp;</span>
                                {{ __('language.available') }}
                            </span>
                            <span class="mr-3">
                                <span class="badge badge-secondary">&nbsp;</span>
                                {{ __('language.sold') }}
                            </span>
                        </div>
                    </div>

                    <div class="card-body">
                        <p class="text-muted mb-3">{!! __('language.land_card_description') !!}</p>

                        {{-- Bọc grid để vẽ rubberBand --}}
                        <div id="gridWrap" class="position-relative">
                            <div id="rubberBand"></div>

                            {{-- Header cột --}}
                            <div class="seat-grid mb-2"
                                style="grid-template-columns:48px repeat({{ $land->width }}, 42px);">
                                <div class="seat-col-label text-muted text-center">#</div>
                                @for ($x = 1; $x <= $land->width; $x++)
                                    <div class="seat-col-label text-muted text-center">{{ $x }}</div>
                                @endfor
                            </div>

                            {{-- Hàng ô --}}
                            @foreach ($rows as $y => $row)
                                @php $rowByX = $row->keyBy('x'); @endphp
                                <div class="seat-grid mb-1"
                                    style="grid-template-columns:48px repeat({{ $land->width }}, 42px);">
                                    <div class="seat-row-label text-center font-weight-bold">{{ rowLabel($y) }}</div>

                                    @for ($x = 1; $x <= $land->width; $x++)
                                        @php
                                            $p = $rowByX->get($x);
                                            if (!$p) {
                                                echo '<button type="button" class="btn btn-sm btn-outline-light seat" disabled title="(Chưa tạo)"></button>';
                                                continue;
                                            }
                                            $sold = $p->status === 'sold';
                                            $btnClass = $sold ? 'btn-secondary' : 'btn-outline-success';
                                            // Tooltip content
                                            $cellCode = rowLabel($y) . $x;
                                            $plantName = data_get($p, 'currentCultivation.plant.name', '—');
                                            $stageName = data_get($p, 'currentCultivation.stage.name', '—');
                                            $stateText = data_get($p, 'currentCultivation.status.name', '—');
                                            $title = __('language.land_cell_title', [
                                                'cell' => $cellCode,
                                                'name' => $plantName,
                                                'process' => $stageName,
                                                'status' => $stateText,
                                            ]);
                                        @endphp

                                        <button type="button" class="btn btn-sm {{ $btnClass }} seat"
                                            data-toggle="tooltip" data-placement="top" data-html="true"
                                            title="{!! $title !!}" data-plot="{{ json_encode($p) }}"
                                            data-code="{{ $cellCode }}" onclick="seatClick(event, this)">
                                            <i class="fas fa-seedling"></i>
                                        </button>
                                    @endfor
                                </div>
                            @endforeach
                        </div> {{-- /gridWrap --}}
                    </div>
                </div>
            </div>
            <div class="col-xl-2">
                <div id="bulk-actions" class="action-sticky">
                    <button class="btn btn-primary px-4" id="btnBulkAssign" disabled>
                        <i class="fas fa-layer-group mr-1"></i> Gán <span id="selCount">0</span> ô
                    </button>
                    <button class="btn btn-outline-secondary" id="btnClearSelection" disabled>
                        {{ __('language.unselect') }}
                    </button>
                </div>
            </div>
        </div>
    </section>
    {{-- Modal Assign HÀNG LOẠT --}}
    <x-modal id="bulkModal" header="{{ __('language.land_assign_for_user') }}" center="true" size="lg">
        <form class="w-100" id="bulkForm" method="POST" action="{{ route('lands.plots.assign') }}">
            @csrf
            <div class="mb-3">
                <div class="text-muted"><span id="bulkSummary">—</span></div>
                <div class="mt-1" id="bulkList"></div>
            </div>
            <div id="bulkPlotIdsContainer"></div>

            <div class="form-group">
                <x-user-select name="user_id" :placeholder="trans('language.choose_account')" />
                <span class="text-sm text-danger ml-2"></span>
            </div>
            <div class="form-group" x->
                <label>{{ __('language.start_date') }}</label>
                <input id="b_start_at" class="form-control" type="text" name="start_at" data-picker="date" autocomplete="off">
                <span class="text-sm text-danger ml-2"></span>
            </div>
            <div class="form-group">
                <label>{{ __('language.end_date') }}</label>
                <input id="b_end_at" class="form-control" type="text" name="end_at" data-picker="date" autocomplete="off">
                <span class="text-sm text-danger ml-2"></span>
            </div>
            <div class="form-group">
                <label>{{ __('language.choose_exprired') }}</label>
                <select id="b_quickDuration" class="form-control">
                    <option value="3">{{ __('language.months', ['month' => 3]) }}</option>
                    <option value="6">{{ __('language.months', ['month' => 6]) }}</option>
                    <option value="12" selected>
                        {{ __('language.months', ['month' => 12]) }} ({{ __('language.default') }})
                    </option>
                    <option value="24">{{ __('language.months', ['month' => 24]) }}</option>
                    <option value="custom">{{ __('language.self_enter') }}</option>
                </select>
                <span class="text-sm text-danger ml-2"></span>
            </div>
        </form>
        <x-slot:footer>
            <button type="submit" class="btn btn-primary" id="btnSubmitBulk" form="bulkForm">
                {{ __('language.land_assign') }}
            </button>
            <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                {{ __('language.cancel') }}
            </button>
        </x-slot:footer>
    </x-modal>
@stop

@section('js_library')
    @include('partials.script-library', ['datepicker' => true, 'select2' => true])
@stop
