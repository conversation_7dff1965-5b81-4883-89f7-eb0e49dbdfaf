<?php

namespace App\Http\Requests\Plot;

use Illuminate\Foundation\Http\FormRequest;

class PlotAssignRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'plot_ids' => ['required', 'array'],
            'plot_ids.*' => ['integer', 'exists:plots,id'],
            'user_id'  => ['required', 'exists:users,id'],
            'start_at' => ['nullable', 'date'],
            'end_at'   => ['nullable', 'date', 'after_or_equal:start_at'],
        ];
    }
}
