<?php

namespace App\Logics;

use App\Enums\NotificationStatus;
use App\Jobs\SendNotificationJob;
use App\Models\Notification;
use App\Models\NotificationUser;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class NotificationManager
{
    // Max users per job dispatch
    private const CHUNK_SIZE = 1000;

    /**
     * Send notification to users and save into DB.
     */
    public function sendToUsers(
        array $userIds,
        int $notificationTypeId,
        string $title,
        string $body,
        array $data = [],
    ) {
        try {
            // Create record notification
            $notification = Notification::create([
                'notification_type_id' => $notificationTypeId,
                'title' => $title,
                'body' => $body,
                'data' => $data,
            ]);

            // Chunk Dispatch job to queue
            $userChunks = array_chunk($userIds, self::CHUNK_SIZE);

            $now = now();
            foreach ($userChunks as $chunk) {
                DB::beginTransaction();
                // Create default user notification record
                $notificationUsers = [];
                foreach ($chunk as $userId) {
                    $notificationUsers[] = [
                        'notification_id' => $notification->id,
                        'user_id' => $userId,
                        'status' => NotificationStatus::SENT,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }
                NotificationUser::insert($notificationUsers);
                DB::commit();
            }
            // send notification will job
            dispatch(new SendNotificationJob($userIds, $title, $body, $data));

            return $notification;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get user notifications with filter.
     */
    public function getUserNotifications(int $userId, Request $request)
    {
        $query = NotificationUser::select([
            'notification_users.status as notification_status',
            'notifications.id as notification_id',
            'notifications.title',
            'notifications.body',
            'notifications.data',
            'notifications.created_at',
            'notifications.notification_type_id',
        ])
            ->join('notifications', 'notifications.id', '=', 'notification_users.notification_id')
            ->where('notification_users.user_id', $userId)
            ->orderByDesc('notification_users.created_at');

        $status = $request->query('status');
        if (!empty($status)) {
            $query->where('notification_users.status', $status);
        }

        $perPage = (int) $request->query('per_page', PER_PAGE);

        return $query->paginate($perPage);
    }

    /**
     * Mark notifications as read for a user.
     */
    public function markAsRead(int $userId, Request $request): int
    {
        try {
            DB::beginTransaction();
            $notificationId = $request->input('notification_id');
            $query = NotificationUser::where('user_id', $userId);
            if (!empty($notificationId)) {
                $query->where('notification_id', $notificationId);
            }
            $updated = $query->update([
                'status'  => NotificationStatus::READ,
                'read_at' => now(),
            ]);
            DB::commit();
            return $updated;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
