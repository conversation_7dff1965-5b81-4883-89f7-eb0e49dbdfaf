<?php

namespace Database\Seeders;

use App\Enums\PaymentChannel;
use App\Enums\PaymentStatus;
use App\Enums\OrderStatus;
use App\Models\Order;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting to seed payments...');

        // Get all orders
        $orders = Order::all();

        if ($orders->isEmpty()) {
            $this->command->error('No orders found! Please run OrderSeeder first.');
            return;
        }

        $payments = [];
        $totalPayments = 0;

        foreach ($orders as $order) {
            // Determine number of payments for this order
            $paymentCount = $this->determinePaymentCount($order);

            // Debug: Show payment count for first few orders
            if ($totalPayments < 10) {
                $this->command->info("Order {$order->id} ({$order->status->value}) will have {$paymentCount} payments");
            }

            for ($i = 1; $i <= $paymentCount; $i++) {
                $payment = $this->createPaymentForOrder($order, $i, $paymentCount);

                // Debug: Show payment details for first few
                if ($totalPayments < 10) {
                    $this->command->info("  Payment {$i}/{$paymentCount}: {$payment['status']}");
                }

                $payments[] = $payment;
                $totalPayments++;

                // Insert in batches of 200 to avoid memory issues
                if (count($payments) >= 200) {
                    DB::table('payments')->insert($payments);
                    $payments = [];
                    $this->command->info("Inserted batch of payments. Progress: {$totalPayments}");
                }
            }
        }

        // Insert remaining payments
        if (!empty($payments)) {
            DB::table('payments')->insert($payments);
        }

        // Update latest_payment_id for all orders
        $this->command->info('Updating latest_payment_id for orders...');
        $this->updateLatestPaymentIds();

        $this->command->info("Successfully created {$totalPayments} payments for {$orders->count()} orders!");
    }

    /**
     * Determine how many payments an order should have
     */
    private function determinePaymentCount(Order $order): int
    {
        $rand = fake()->numberBetween(1, 100);

        // Orders that are not paid typically have fewer payment attempts
        if (in_array($order->status->value, [OrderStatus::PENDING->value, OrderStatus::EXPIRED->value, OrderStatus::CANCELED->value])) {
            if ($rand <= 70) return 1;
            if ($rand <= 95) return 2;
            return 3;
        }

        // Failed orders might have more attempts
        if ($order->status->value === OrderStatus::FAILED->value) {
            if ($rand <= 20) return 1;
            if ($rand <= 60) return 2;
            if ($rand <= 90) return 3;
            return 4;
        }

        // Paid orders usually have 1 successful payment, but might have had failed attempts first
        if ($order->status->value === OrderStatus::PAID->value) {
            if ($rand <= 60) return 1;
            if ($rand <= 85) return 2;
            if ($rand <= 95) return 3;
            return 4;
        }

        // Processing orders typically have 1 ongoing payment
        return 1;
    }

    /**
     * Create a payment for a specific order
     */
    private function createPaymentForOrder(Order $order, int $attemptNo, int $totalAttempts): array
    {
        $channel = fake()->randomElement(PaymentChannel::cases());

        // Determine payment status based on order status and attempt number
        $status = $this->determinePaymentStatus($order, $attemptNo, $totalAttempts);

        $createdAt = fake()->dateTimeBetween($order->created_at, 'now');
        $expiresAt = fake()->dateTimeBetween($createdAt, '+1 day');

        // For multiple payments, space them out chronologically
        if ($attemptNo > 1) {
            $previousCreatedAt = $createdAt;
            $createdAt = fake()->dateTimeBetween($order->created_at, 'now');
            $expiresAt = fake()->dateTimeBetween($createdAt, '+1 day');
        }

        $paidAt = null;
        $providerTransactionId = null;

        // Set paid_at and transaction ID for successful payments
        if ($status === PaymentStatus::SUCCESS) {
            $maxPaidDate = $order->paid_at && $order->paid_at > $createdAt ? $order->paid_at : $expiresAt;
            $paidAt = fake()->dateTimeBetween($createdAt, $maxPaidDate);
            $providerTransactionId = $this->generateProviderTransactionId($channel);
        }

        // Generate meta data based on channel and status
        $meta = $this->generatePaymentMeta($channel, $status);

        return [
            'id' => fake()->uuid(),
            'order_id' => $order->id,
            'channel' => $channel->value,
            'status' => $status->value,
            'amount' => $order->amount,
            'currency' => $order->currency,
            'attempt_no' => $attemptNo,
            'provider_transaction_id' => $providerTransactionId,
            'expires_at' => $expiresAt,
            'paid_at' => $paidAt,
            'meta' => json_encode($meta),
            'created_at' => $createdAt,
            'updated_at' => fake()->dateTimeBetween($createdAt, 'now'),
        ];
    }

    /**
     * Determine payment status based on order status and attempt number
     */
    private function determinePaymentStatus(Order $order, int $attemptNo, int $totalAttempts): PaymentStatus
    {
        // For paid orders, the last payment should be successful
        if ($order->status->value === OrderStatus::PAID->value && $attemptNo === $totalAttempts) {
            return PaymentStatus::SUCCESS;
        }

        // For paid orders with multiple attempts, earlier attempts should be failed
        if ($order->status->value === OrderStatus::PAID->value && $attemptNo < $totalAttempts) {
            return fake()->randomElement([PaymentStatus::FAILED, PaymentStatus::EXPIRED, PaymentStatus::CANCELED]);
        }

        // For processing orders, payment should be pending or init
        if ($order->status->value === OrderStatus::PROCESSING->value) {
            return fake()->randomElement([PaymentStatus::PENDING, PaymentStatus::INIT]);
        }

        // For failed orders, payments should be failed
        if ($order->status->value === OrderStatus::FAILED->value) {
            return PaymentStatus::FAILED;
        }

        // For expired orders, payments should be expired
        if ($order->status->value === OrderStatus::EXPIRED->value) {
            return PaymentStatus::EXPIRED;
        }

        // For canceled orders, payments should be canceled
        if ($order->status->value === OrderStatus::CANCELED->value) {
            return PaymentStatus::CANCELED;
        }

        // For pending orders, payments are usually init or pending
        return fake()->randomElement([PaymentStatus::INIT, PaymentStatus::PENDING]);
    }

    /**
     * Generate provider transaction ID based on channel
     */
    private function generateProviderTransactionId(PaymentChannel $channel): string
    {
        return match($channel) {
            PaymentChannel::VNPAY => 'VNP' . fake()->numerify('########') . fake()->numerify('########'),
            PaymentChannel::MOMO => 'MOMO' . fake()->numerify('##########'),
            default => fake()->numerify('############'),
        };
    }

    /**
     * Generate meta data based on channel and status
     */
    private function generatePaymentMeta(PaymentChannel $channel, PaymentStatus $status): array
    {
        return match($channel) {
            PaymentChannel::VNPAY => [
                'vnp_TxnRef' => fake()->numerify('########'),
                'vnp_OrderInfo' => 'Thanh toan don hang',
                'vnp_BankCode' => fake()->randomElement(['NCB', 'BIDV', 'VCB', 'TCB', 'MB', 'ACB', 'TPB', 'VTB']),
                'vnp_CardType' => fake()->randomElement(['ATM', 'QRCODE', 'CREDIT']),
                'vnp_ResponseCode' => $status === PaymentStatus::SUCCESS ? '00' : fake()->randomElement(['05', '06', '07', '09', '10', '99']),
                'vnp_TransactionNo' => $status === PaymentStatus::SUCCESS ? fake()->numerify('#############') : null,
                'vnp_BankTranNo' => $status === PaymentStatus::SUCCESS ? fake()->numerify('##########') : null,
            ],
            PaymentChannel::MOMO => [
                'partnerCode' => 'MOMOBKUN20180529',
                'requestId' => fake()->uuid(),
                'orderId' => fake()->numerify('########'),
                'resultCode' => $status === PaymentStatus::SUCCESS ? 0 : fake()->randomElement([1000, 1001, 1002, 1003, 9000]),
                'message' => $status === PaymentStatus::SUCCESS ? 'Successful.' : 'Payment failed.',
                'payType' => fake()->randomElement(['qr', 'napas', 'credit']),
                'transId' => $status === PaymentStatus::SUCCESS ? fake()->numerify('#############') : null,
                'responseTime' => now()->timestamp * 1000,
            ],
            default => []
        };
    }

    /**
     * Update latest_payment_id for all orders based on their most recent payment
     */
    private function updateLatestPaymentIds(): void
    {
        // Get all orders with their latest payment
        $ordersWithLatestPayment = DB::table('orders')
            ->leftJoin('payments', function($join) {
                $join->on('orders.id', '=', 'payments.order_id')
                     ->whereRaw('payments.id = (
                         SELECT p2.id
                         FROM payments p2
                         WHERE p2.order_id = orders.id
                         ORDER BY p2.created_at DESC, p2.id DESC
                         LIMIT 1
                     )');
            })
            ->select('orders.id as order_id', 'payments.id as latest_payment_id')
            ->get();

        $updatedCount = 0;
        foreach ($ordersWithLatestPayment as $orderPayment) {
            if ($orderPayment->latest_payment_id) {
                DB::table('orders')
                    ->where('id', $orderPayment->order_id)
                    ->update(['latest_payment_id' => $orderPayment->latest_payment_id]);
                $updatedCount++;
            }
        }

        $this->command->info("Updated latest_payment_id for {$updatedCount} orders.");
    }
}
