<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],
    'facebook' => [
        'client_id' => env('FACEBOOK_CLIENT_ID'),
        'client_secret' => env('FACEBOOK_CLIENT_SECRET'),
        'redirect' => env('FACEBOOK_REDIRECT_URI', 'https://dummy.local/callback')
    ],
    'apple' => [
        'client_id'     => env('APPLE_CLIENT_ID'),
        'team_id'       => env('APPLE_TEAM_ID'),
        'key_id'        => env('APPLE_KEY_ID'),
        'private_key'   => env('APPLE_PRIVATE_KEY'),
        'redirect'      => env('APPLE_REDIRECT_URI', 'https://dummy.local/callback'),
    ],
    'zalo' => [
        'client_id'     => env('ZALO_APP_ID'),
        'client_secret' => env('ZALO_APP_SECRET'),
        'redirect'      => env('ZALO_REDIRECT_URI', 'https://dummy.local/callback'),
    ],
    'google' => [
        'client_id'     => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect'      => env('GOOGLE_REDIRECT_URI', 'https://beefarm.beetechdev.vn/'),
    ],

    'sms' => [
        'url' => env('SMS_API_URL'),
        'api_user' => env('SMS_API_USER'),
        'api_pass' => env('SMS_API_PASS'),
        'agent_id' => env('SMS_AGENT_ID'),
        'username' => env('SMS_USERNAME'),
        'label_id' => env('SMS_BRAND_LABELID'),
        'contract_id' => env('SMS_CONTRACT_ID'),
        'otp_template_id' => env('SMS_OTP_TEMPLATE_ID'),
    ],
];
