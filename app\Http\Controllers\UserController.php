<?php

namespace App\Http\Controllers;

use App\Helpers\FilterHelper;
use App\Enums\FilterType;
use App\Http\Requests\UserRequest;
use App\Logics\UserManager;
use App\Models\Province;
use App\Models\Role;
use App\Models\User;
use App\Models\Ward;
use App\Services\AvatarService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Vinkla\Hashids\Facades\Hashids;

class UserController extends Controller
{
    private $userManager;
    private $filterHelper;

    public function __construct(
        UserManager $userManager,
        FilterHelper $filterHelper
    ) {
        $this->userManager = $userManager;
        $this->filterHelper = $filterHelper;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Get all users
        $users = $this->userManager->getUserList($request);

        // Pagination
        $perPage = $request->has('per_page') ? $request->input('per_page') : PER_PAGE;
        $users = $users->paginate($perPage);

        if ($redirect = $this->redirectInvalidPage($users, $request)) {
            return $redirect;
        }

        // Filter configuration - using enum to manage types with createConfig
        $filterConfig = [
            FilterType::createConfig('id', FilterType::IDS, ['prefix' => '#']),
            FilterType::createConfig('keyword', FilterType::TEXT),
            FilterType::createConfig('email', FilterType::TEXT),
            FilterType::createConfig('phone', FilterType::TEXT),
            FilterType::createConfig('gender', FilterType::ARRAY, [
                'mapping' => trans('language.genders')
            ])
        ];
        $isFilter = $this->filterHelper->renderFilterBadges($request, $filterConfig);

        return view('user.index', [
            'users' => $users,
            'is_filter' => $isFilter,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $isAdmin = auth()->user()->hasRole(Role::ROLE_SYSTEM_MANAGER);
        $canEdit = auth()->user()->hasAnyRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER]);
        $allowedRoles = Role::select('name')
            ->when(!$isAdmin, function ($q) {
                $q->where('name', '!=', Role::ROLE_SYSTEM_MANAGER);
            })
            ->orderBy('id', 'asc')
            ->get();
        $provinces = Province::select('province_code', 'name')
            ->orderBy('id')->get();
        $wards = [];

        return view('user.edit', [
            'allowedRoles' => $allowedRoles,
            'canEdit' => $canEdit,
            'prefectures' => $provinces,
            'communes' => $wards,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserRequest $request)
    {
        $this->userManager->createUser($request, true);

        return redirect()->route('user.index')->with([
            'status_succeed' => trans('message.success')
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = User::find($id);

        // Return error message if user not exist
        if ($user === null) {
            return back()->with([
                'status_failed' => trans('message.user_not_exist')
            ]);
        }
        $userRoles = $user->roles->pluck('name')->toArray();
        $isAdmin = auth()->user()->hasRole(Role::ROLE_SYSTEM_MANAGER);
        $canEdit = $user->deleted_at == null
            && auth()->user()->hasAnyRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER]);
        $allowedRoles = Role::select('name')
            ->when(!$isAdmin, function ($q) {
                $q->where('name', '!=', Role::ROLE_SYSTEM_MANAGER);
            })
            ->orderBy('id', 'asc')
            ->get();
        $provinces = Province::select('province_code', 'name')
            ->orderBy('id')->get();
        $wards = $user->province_code == null ? [] :
            Ward::select('ward_code', 'name')
                ->where('province_code', $user->province_code)
                ->orderBy('id')->get();

        return view('user.edit', [
            'user' => $user,
            'userRoles' => $userRoles,
            'allowedRoles' => $allowedRoles,
            'canEdit' => $canEdit,
            'prefectures' => $provinces,
            'communes' => $wards,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserRequest $request, string $id)
    {
        $result = $this->userManager->updateUser($request, $id);

        if (!$result) {
            return back()->with([
                'status_failed' => trans('message.user_not_exist')
            ]);
        }

        return back()->with([
            'status_succeed' => trans('message.success')
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $result = $this->userManager->deleteUser($id);

        if (!$result) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.user_not_exist'),
                ],
            ];
        }

        return [
            'status' => Response::HTTP_OK,
            'msg' => [
                'title' => trans('language.success'),
                'text' => trans('message.delete_user_succeed'),
            ],
        ];
    }

    /**
     * Get the avatar image for a user.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function getAvatar(Request $request, $id)
    {
        // Validate and decode ID
        $userId = Hashids::decode($id)[0] ?? null;

        if (!$userId) {
            abort(404);
        }

        // Validate avatar size
        $avatarSize = $request->input('size', AvatarService::DEFAULT_SIZE);
        if (!in_array($avatarSize, AvatarService::allowedSizes())) {
            abort(400, 'Invalid size parameter');
        }

        $image = $this->userManager->getAvatar($userId, $avatarSize);

        return $image;
    }

    /**
     * Get users for AJAX select2 dropdown
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getListUser(Request $request)
    {
        $userId = auth()->user()->id;
        $stringHelper = new \App\Helpers\StringHelper();
        $keyword_search = $stringHelper->formatStringWhereLike($request->q ?? '');

        // Pagination parameters
        $perPage = $request->input('per_page', PER_PAGE);
        $page = $request->input('page', 1);

        // Get all users with pagination
        $query = User::select(['users.id', 'users.name', 'users.email', 'users.phone', 'users.avatar']);

        if ($request->has('deleted') && $request->deleted) {
            $query = $query->onlyTrashed();
        }

        // Search by keyword
        if (!empty($keyword_search)) {
            $query = $query->where(function($query) use($keyword_search) {
                $query->orWhere('users.id', 'LIKE', '%'.$keyword_search.'%')
                    ->orWhere('users.email', 'LIKE', '%'.$keyword_search.'%')
                    ->orWhere('users.name', 'LIKE', '%'.$keyword_search.'%')
                    ->orWhere('users.phone', 'LIKE', '%'.$keyword_search.'%');
            });
        }

        // Apply ordering and pagination
        $members = $query->orderByRaw('FIELD(users.id, '.$userId.') DESC, users.name ASC')
            ->paginate($perPage, $page);

        $item = [];
        foreach ($members as $member) {
            // Use avatar URL if user has avatar, otherwise use default
            $avatarUrl = null;
            if (!empty($member->avatar)) {
                $avatarUrl = $member->avatarUrl('sm');
            }

            $item[] = [
                'id' => $member->id,
                'name' => $member->name ?? '',
                'email' => $member->email ?? '',
                'phone' => $member->phone ?? '',
                'avatar' => $avatarUrl, // null if no avatar, URL if has avatar
            ];
        }

        $result = [
            'incomplete_results' => $members->hasMorePages(),
            'items' => $item,
            'total_count' => $members->total(),
            'page' => $members->currentPage(),
            'per_page' => $members->perPage(),
            'has_more' => $members->hasMorePages()
        ];

        return response()->json($result);
    }
}
