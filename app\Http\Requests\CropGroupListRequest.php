<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CropGroupListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            /**
             * Parent crop group ID.
             * @example 1
             */
            'parent_id' => ['nullable', 'integer'],
            /**
             * Search keyword.
             * @example Rau ăn lá
             */
            'search' => ['nullable', 'string', 'max:255'],
            /**
             * Number of records per page.
             * @default 20
             * @example 30
             */
            'per_page' => ['nullable', 'integer', 'min:1', 'max:10000'],
        ];
    }
}

