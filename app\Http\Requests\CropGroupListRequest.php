<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CropGroupListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            /**
             * Parent crop group ID.
             * @example 1
             */
            'parent_id' => ['nullable', 'integer'],
            /**
             * Search keyword.
             * @example Rau ăn lá
             */
            'search' => ['nullable', 'string', 'max:255'],
            /**
             * Number of records per page.
             * @default 20
             * @example 30
             */
            'per_page' => ['nullable', 'integer', 'min:1', 'max:10000'],
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'parent_id.integer' => 'ID nhóm cha phải là số nguyên.',
            'search.string' => 'Từ khóa tìm kiếm phải là chuỗi ký tự.',
            'search.max' => 'Từ khóa tìm kiếm không được vượt quá 255 ký tự.',
            'per_page.integer' => 'Số bản ghi trên trang phải là số nguyên.',
            'per_page.min' => 'Số bản ghi trên trang phải lớn hơn 0.',
            'per_page.max' => 'Số bản ghi trên trang không được vượt quá 10000.',
        ];
    }
}

