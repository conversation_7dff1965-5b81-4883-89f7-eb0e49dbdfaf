<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CropGroupListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            /**
             * Parent crop group ID.
             * @example 1
             */
            'parent_id' => ['nullable', 'integer'],
            /**
             * Search keyword.
             * @example Rau ăn lá
             */
            'search' => ['nullable', 'string', 'max:255'],
            /**
             * Number of records per page.
             * @default 20
             * @example 30
             */
            'per_page' => ['nullable', 'integer', 'min:1', 'max:10000'],
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'parent_id.integer' => __('validation.integer', ['attribute' => __('attributes.parent_id')]),
            'search.string' => __('validation.string', ['attribute' => __('attributes.search')]),
            'search.max' => __('validation.max.string', ['attribute' => __('attributes.search'), 'max' => 255]),
            'per_page.integer' => __('validation.integer', ['attribute' => __('attributes.per_page')]),
            'per_page.min' => __('validation.min.numeric', ['attribute' => __('attributes.per_page'), 'min' => 1]),
            'per_page.max' => __('validation.max.numeric', ['attribute' => __('attributes.per_page'), 'max' => 10000]),
        ];
    }
}

