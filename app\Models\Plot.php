<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Plot extends Model
{
    protected $fillable = ['land_id', 'x', 'y', 'status', 'purchased_at', 'owner_user_id', 'assigned_until'];

    protected $casts = ['purchased_at' => 'datetime', 'assigned_until' => 'date'];

    public function land()
    {
        return $this->belongsTo(Land::class);
    }

    public function assignments()
    {
        return $this->hasMany(PlotAssignment::class);
    }

    public function scopeAvailable($q)
    {
        return $q->where('status', 'available');
    }

    public function currentAssignment()
    {
        return $this->hasOne(PlotAssignment::class)
            ->where(function ($q) {
                $q->whereNull('start_at')->orWhereDate('start_at', '<=', today());
            })
            ->where(function ($q) {
                $q->whereNull('end_at')->orWhereDate('end_at', '>=', today());
            })
            ->orderByRaw('(end_at IS NULL) DESC, end_at DESC');
    }

    public function getStatusAttribute()
    {
        if ($this->relationLoaded('currentAssignment')) {
            return $this->currentAssignment ? 'sold' : 'available';
        }
        return $this->currentAssignment()->exists() ? 'sold' : 'available';
    }

    public function getAssignedUntilAttribute()
    {
        return optional($this->currentAssignment)->end_at;
    }

    public function getOwnerUserIdAttribute()
    {
        return optional($this->currentAssignment)->user_id;
    }
}
