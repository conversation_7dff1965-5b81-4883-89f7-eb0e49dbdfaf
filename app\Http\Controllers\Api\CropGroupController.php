<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\CropGroupListRequest;
use App\Logics\CropGroupManager;

class CropGroupController extends Controller
{
    protected $cropGroupManager;

    /**
     * Constructor
     * @param CropGroupManager $cropGroupManager
     */
    public function __construct(CropGroupManager $cropGroupManager)
    {
        $this->cropGroupManager = $cropGroupManager;
    }

    /**
     * List crop groups
     * @param CropGroupListRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @response App\Http\Resources\CropGroupResource[]
     * @paginated
     */
    public function list(CropGroupListRequest $request)
    {
        // Get validated data
        $validatedData = $request->validated();

        // Get language_id from request attributes (set by SetLocaleFromHeader middleware)
        $languageId = $request->attributes->get('language_id');

        // Get per_page value
        $perPage = $validatedData['per_page'] ?? PER_PAGE;

        // Get crop groups query from manager
        $query = $this->cropGroupManager->list($validatedData, $languageId);

        // Paginate
        $cropGroups = $query->paginate($perPage);

        // Format data
        $formattedData = $this->formatCropGroups($cropGroups);

        return response()->json([
            "message" => "Success",
            "current_page" => $cropGroups->currentPage(),
            "total_page" => $cropGroups->lastPage(),
            "per_page" => (int)$cropGroups->perPage(),
            "total" => $cropGroups->total(),
            "data" => $formattedData,
        ]);
    }

    /**
     * Format crop groups data
     * @param \Illuminate\Pagination\LengthAwarePaginator $cropGroups
     * @return array
     */
    private function formatCropGroups($cropGroups): array
    {
        return $cropGroups->getCollection()->map(function ($cropGroup) {
            // Get the first locale (already filtered by language_id in eager load)
            $locale = $cropGroup->locales->first();

            return [
                /**
                 * Crop group ID.
                 * @var int
                 * @example 1
                 */
                "id" => $cropGroup->id,
                /**
                 * Crop group name.
                 * @var string
                 * @example "Rau ăn lá"
                 */
                "name" => $locale?->name ?? '',
                /**
                 * Crop group description.
                 * @var string
                 * @example "Nhóm rau ăn lá bao gồm các loại cây được trồng chủ yếu để thu hoạch phần lá xanh."
                 */
                "description" => $locale?->description ?? '',
                /**
                 * Parent crop group ID.
                 * @var int|null
                 * @example null
                 */
                "parent_id" => $cropGroup->parent_id,
            ];
        })->toArray();
    }
}
