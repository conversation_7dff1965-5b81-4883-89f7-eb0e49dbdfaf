<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;
use Vinkla\Hashids\Facades\Hashids;

class User extends Authenticatable implements JWTSubject
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;
    use SoftDeletes;
    use HasRoles;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'social_ids' => 'array'
        ];
    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    /**
     * Get the user's hashed ID.
     *
     * @return string
     */
    public function getUuidAttribute()
    {
        return Hashids::encode($this->id);
    }

    /**
     * Get the URL to the user's avatar.
     *
     * @param string $size The size of the avatar ('sm', 'md', 'lg').
     * @return string The URL to the user's avatar.
     */
    public function avatarUrl(string $size = 'sm'): string
    {
        return route('user.avatar', [
            'id'   => $this->uuid,
            'size' => $size,
            'v'    => $this->updated_at->timestamp ?? now()->timestamp,
        ]);
    }

    /**
     * Plots assigned for user
     *
     * @return BelongsToMany
     */
    public function plots(): BelongsToMany
    {
        $pivot = (new PlotAssignment)->getTable();
        return $this->belongsToMany(Plot::class, $pivot);
    }

    /**
     * Get the user's language.
     *
     * @return BelongsTo
     */
    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class, 'language_id');
    }
}
