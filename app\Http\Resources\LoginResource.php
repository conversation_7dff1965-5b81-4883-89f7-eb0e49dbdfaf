<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LoginResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /**
             * User access token.
             * @var string
             * @example "xxx.yyy.zzz"
             */
            'access_token' => $this->access_token,
            /**
             * User Refresh token.
             * @var string
             * @example "xxx.yyy.zzz"
             */
            'refresh_token' => $this->refresh_token,
            /**
             * User ID.
             * @var int
             * @example "1"
             */
            'user_id' => $this->user_id,
            /**
             * User name.
             * @var string
             * @example "Nguyen Van A"
             */
            'name' => $this->name,
            /**
             * User language ID.
             * @var int
             * @example 1
             */
            'language_id' => $this->language_id,
            /**
             * User email.
             * @var string
             * @example "<EMAIL>"
             */
            'email' => $this->email,
            /**
             * User phone number.
             * @var string
             * @example "0123456789"
             */
            'phone' => $this->phone,
            /**
             * Ussr roles.
             * @var array<string>
             * @example ["SystemManager"]
             */
            'roles' => $this->roles,
            /**
             * User token expriration time.
             * @var int
             * @example 3600
             */
            'expires_in' => $this->expires_in,
        ];
    }
}
