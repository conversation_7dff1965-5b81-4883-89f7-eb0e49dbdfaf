<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlantResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /**
             * Pland Id.
             * @var int
             * @example 102
             */
            'id' => $this->id,
            /**
             * Pland Name.
             * @var string
             * @example Cà chua
             */
            'name' => $this->name,
            /**
             * Pland Category.
             * @var string
             * @example Rau
             */
            'category' => $this->category,
            /**
             * Start date.
             * @var string
             * @example 2025-09-12
             */
            'start_at' => $this->start_at,
            /**
             * Remain growth time (seconds).
             * @var int
             * @example 5836545
             */
            'remain_grownth' => $this->remain_grownth,
            /**
             * Plant states (0: SEED, 1: GERMINATED, 2: SEEDLING, 3: GROWING, 4: FLOWERING, 5: FRUITING, 6: RIP<PERSON>, 7: HARVESTED, 8: WILTING, 9: DEAD, 10: PEST_INFESTED, 11: WEEDY, 12: DISEASED, 13: DROUGHT, 14: FLOODED).
             * @var array<int>
             * @example [4, 10]
             */
            'plant_states' => $this->current_states,
            /**
             * Current stage of plant.
             * @var int
             * @example 2
             */
            'grownth_stage' => $this->current_stage,
            /**
             * Remain time next stage (seconds).
             * @var int
             * @example 566145
             */
            'remain_next_stage' => $this->current_stage,
        ];
    }
}
