<?php

use App\Enums\DeviceType;
use App\Models\UserDevice;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_devices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('device_token', 255)->unique();
            $table->unsignedTinyInteger('device_type')->default(DeviceType::ANDROID)->comment('1=ANDROID, 2=IOS, 3=WEB');
            $table->unsignedTinyInteger('status')->default(UserDevice::STATUS_OFFLINE)->comment('0=OFFLINE, 1=ONLINE');
            $table->timestamps();

            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_devices');
    }
};
