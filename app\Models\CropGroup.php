<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class CropGroup extends Model
{
    use SoftDeletes;

    protected $table = 'crop_groups';

    protected $fillable = ['parent_id',];

    /**
     * Get the parent crop group.
     * @return BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(CropGroup::class, 'parent_id');
    }

    /**
     * Get the children crop groups.
     * @return HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(CropGroup::class, 'parent_id');
    }

    /**
     * Get the crop group locales.
     * @return HasMany
     */
    public function locales(): HasMany
    {
        return $this->hasMany(CropGroupLocale::class);
    }

    /**
     * Get the crops in the group.
     * @return HasMany
     */
    public function crops(): HasMany
    {
        return $this->hasMany(Crop::class, 'crop_group_id');
    }

    /**
     * Scope to include localized data for a specific language
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $languageId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithLocale($query, $languageId)
    {
        return $query->with([
            'locales' => function($q) use ($languageId) {
                $q->forLanguage($languageId);
            }
        ]);
    }

    /**
     * Scope to exclude deleted records
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->whereNull('deleted_at');
    }
}
