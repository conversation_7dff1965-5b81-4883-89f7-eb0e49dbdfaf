@if (isset($crops) && count($crops) > 0)
    <div class="table-projects table-responsive" id="double-scroll">
        <table class="table table-hover table-valign-middle min-width-1200">
            <thead class="text-center text-nowrap">
                <tr>
                    <th>{{ __('language.id') }}</th>
                    <th>{{ __('language.crop_name') }}</th>
                    <th>{{ __('language.crop_group') }}</th>
                    <th>{{ __('language.cost') }}</th>
                    <th>{{ __('language.harvest_phases') }}</th>
                    <th>{{ __('language.action') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($crops as $idx => $crop)
                    @php
                        // Get localized name
                        $cropName = $crop->locales->first() ? $crop->locales->first()->name : __('language.na');

                        // Get localized group name
                        $groupName = __('language.na');
                        if ($crop->cropGroup && $crop->cropGroup->locales->first()) {
                            $groupName = $crop->cropGroup->locales->first()->name;
                        }

                        // Format cost display
                        $costDisplay = '';
                        if (isset($crop->cost['gold']) && $crop->cost['gold'] > 0) {
                            $costDisplay .= $crop->cost['gold'] . ' ' . __('language.gold');
                        }
                        if (isset($crop->cost['gem']) && $crop->cost['gem'] > 0) {
                            $costDisplay .=
                                ($costDisplay ? ' / ' : '') . $crop->cost['gem'] . ' ' . __('language.gem');
                        }
                        if (empty($costDisplay)) {
                            $costDisplay = __('language.na');
                        }

                        // Format harvest phases
                        $harvestDisplay = __('language.na');
                        if (is_array($crop->harvest_phases) && count($crop->harvest_phases) > 0) {
                            $phases = [];
                            foreach ($crop->harvest_phases as $phase) {
                                if (isset($phase['name']) && isset($phase['duration'])) {
                                    $phases[] =
                                        $phase['name'] . ' (' . $phase['duration'] . ' ' . __('language.days') . ')';
                                }
                            }
                            if (!empty($phases)) {
                                $harvestDisplay = implode(', ', $phases);
                            }
                        }
                    @endphp
                    <tr>
                        <td class="text-center">{{ $crop->id }}</td>
                        <td>{{ $cropName }}</td>
                        <td>{{ $groupName }}</td>
                        <td class="text-center">{{ $costDisplay }}</td>
                        <td title="{{ $harvestDisplay }}">{{ $harvestDisplay }}</td>
                        <td class="text-center text-nowrap">
                            @if ($crop->deleted_at == null)
                                <a href="{{ route('crops.edit', $crop->id) }}" data-toggle='tooltip' title="{{ __('language.edit') }}"
                                    class="text-md text-primary mr-2"><i class="far fa-pen-alt"></i></a>
                                <a href="{{ route('crops.destroy', $crop->id) }}"
                                    data-toggle='tooltip'
                                    title="{{ __('language.delete') }}"
                                    class="text-md text-danger delete-row-table btn-delete-jb"
                                    data-id="{{ $crop->id }}"
                                    data-title="{{ __('language.delete_crop') }}"
                                    data-text="<span class='text-bee'>ID: {{ $crop->id }}</span> - <strong>{{ $cropName }}</strong>"
                                    data-url="{{ route('crops.destroy', $crop->id) }}"
                                    data-method="DELETE"
                                    data-icon="question"><i class="far fa-trash-alt"></i></a>
                            @endif
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="pb-4">
        {{ $crops->appends($request->query())->links('partials.pagination') }}
    </div>
@else
    @include('partials.no-data-found')
@endif
