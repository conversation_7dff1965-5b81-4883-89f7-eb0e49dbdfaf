import '@/build/scss/pages/lands-grid.scss';
import moment from 'moment';

import { trans } from "../locales/locales";
import { DATE_TIME_FORMATS } from '../constants';
import { getFormData } from '../helper/form';

let language = $("body").data('locales');
const formatDate = DATE_TIME_FORMATS[language].formatDate;

function addMonthsKeepEnd(startDateStr, months) {
    const startDate = moment(startDateStr, formatDate);
    return startDate.add(months, 'month').format(formatDate);
}

// ===== State chọn nhiều =====
const selectedMap = new Map(); // id -> {btn,x,y,status}
let anchorSeat = null;

const $btnBulk = $('#btnBulkAssign'), $btnClear = $('#btnClearSelection'), $selCount = $('#selCount');
function updateBulkToolbar() { const n = selectedMap.size; $selCount.text(n); $btnBulk.prop('disabled', n === 0); $btnClear.prop('disabled', n === 0); }
function clearSelection() { for (const { btn } of selectedMap.values()) { btn.classList.remove('seat-picked'); } selectedMap.clear(); updateBulkToolbar(); anchorSeat = null; }
$('#btnClearSelection').on('click', clearSelection);

function togglePick(btn, pick = true) {
    const { id, x, y, status } = JSON.parse(btn.dataset.plot);
    const code = btn.dataset.code;
    if (pick) {
        if (!selectedMap.has(id)) {
            btn.classList.add('seat-picked');
            selectedMap.set(parseInt(id, 10), { btn, x: parseInt(x, 10), y: parseInt(y, 10), status, code });
        }
    } else {
        if (selectedMap.has(id)) {
            btn.classList.remove('seat-picked'); selectedMap.delete(id);
        }
    }
}

const seatIndex = new Map();
document.querySelectorAll('#gridWrap .seat').forEach(btn => {
    if (btn.disabled) return;
    const { x, y } = JSON.parse(btn.dataset.plot);
    if (x && y) seatIndex.set(`${x},${y}`, btn);
});

function selectRectangle(x1, y1, x2, y2, add = true) {
    const xmin = Math.min(x1, x2), xmax = Math.max(x1, x2), ymin = Math.min(y1, y2), ymax = Math.max(y1, y2);
    if (!add) clearSelection();
    for (let yy = ymin; yy <= ymax; yy++) {
        for (let xx = xmin; xx <= xmax; xx++) {
            const btn = seatIndex.get(`${xx},${yy}`); if (btn) { togglePick(btn, true); }
        }
    }
    updateBulkToolbar();
}
function selectRowRange(y, x1, x2, add = true) { selectRectangle(x1, y, x2, y, add); }

// ===== Click ô (đơn / ctrl / shift) =====
let wasDragging = false;
function seatClick(ev, el) {
    if (wasDragging) { wasDragging = false; return; }
    const plot = JSON.parse(el.dataset.plot);

    const x = parseInt(plot.x, 10), y = parseInt(plot.y, 10);
    const id = parseInt(plot.id, 10);

    if (ev.ctrlKey || ev.metaKey) {
        const picked = selectedMap.has(id);
        togglePick(el, !picked);
        anchorSeat = { x, y };
        updateBulkToolbar();
        return;
    }
    if (ev.shiftKey && anchorSeat) {
        if (anchorSeat.y === y) selectRowRange(y, anchorSeat.x, x, true);
        else selectRectangle(anchorSeat.x, anchorSeat.y, x, y, true);
        return;
    }

    // Click thường -> gán đơn
    openAssignModal(el);
    anchorSeat = { x, y };
}
window.seatClick = seatClick;

// ===== Rubber-band (kéo chọn vùng) =====
const gridWrap = document.getElementById('gridWrap');
const rubber = document.getElementById('rubberBand');
let drag = false, startX = 0, startY = 0, dragAdditive = false, dragStartClient = { x: 0, y: 0 };
const DRAG_THRESHOLD = 5;

gridWrap.addEventListener('mousedown', (e) => {
    drag = true; wasDragging = false;
    dragAdditive = !!(e.ctrlKey || e.metaKey);
    const rect = gridWrap.getBoundingClientRect();
    startX = e.clientX - rect.left + gridWrap.scrollLeft;
    startY = e.clientY - rect.top + gridWrap.scrollTop;
    dragStartClient = { x: e.clientX, y: e.clientY };
    rubber.style.display = 'block';
    rubber.style.left = startX + 'px'; rubber.style.top = startY + 'px';
    rubber.style.width = '0px'; rubber.style.height = '0px';
    e.preventDefault();
});
gridWrap.addEventListener('mousemove', (e) => {
    if (!drag) return;
    const rect = gridWrap.getBoundingClientRect();
    const curX = e.clientX - rect.left + gridWrap.scrollLeft;
    const curY = e.clientY - rect.top + gridWrap.scrollTop;
    const left = Math.min(curX, startX), top = Math.min(curY, startY);
    const width = Math.abs(curX - startX), height = Math.abs(curY - startY);
    rubber.style.left = left + 'px'; rubber.style.top = top + 'px';
    rubber.style.width = width + 'px'; rubber.style.height = height + 'px';
    if (Math.abs(e.clientX - dragStartClient.x) > DRAG_THRESHOLD || Math.abs(e.clientY - dragStartClient.y) > DRAG_THRESHOLD) {
        wasDragging = true;
    }
});
window.addEventListener('mouseup', (e) => {
    if (!drag) return;
    drag = false;
    const moved = Math.abs(e.clientX - dragStartClient.x) > DRAG_THRESHOLD || Math.abs(e.clientY - dragStartClient.y) > DRAG_THRESHOLD;
    if (moved) {
        const rb = rubber.getBoundingClientRect();
        if (!dragAdditive) clearSelection();
        document.querySelectorAll('#gridWrap .seat').forEach(btn => {
            const br = btn.getBoundingClientRect();
            const intersect = !(rb.right < br.left || rb.left > br.right || rb.bottom < br.top || rb.top > br.bottom);
            if (intersect) { togglePick(btn, true); }
        });
        updateBulkToolbar();
    }
    rubber.style.display = 'none';
});

// ===== Modal: Gán ĐƠN =====
function openAssignModal(btn) {
    const $btn = $(btn);
    const plot = $btn.data('plot');
    const code = $btn.data('code');
    const { id, x, y, status } = plot;
    if (plot.current_assignment) {
        const currentAssign = plot.current_assignment;
        const form = $('#bulkForm');

        // Set current user in select
        const userSelect = form.find('[name="user_id"]');
        const user = currentAssign.user;

        const parts = [user.name];
        if (user.email) parts.push(user.email);
        if (user.phone) parts.push(user.phone);
        const optionText = parts.join(' | ');

        userSelect.empty().append(new Option(optionText, user.id, true, true)).trigger('change');

        //Start at
        const startAt = moment(currentAssign.start_at).format(formatDate);
        form.find('[name="start_at"]').val(startAt);
        //End at
        const endAt = moment(currentAssign.end_at).format(formatDate);
        form.find('[name="end_at"]').val(endAt);
        //Custom add time
        $('#b_quickDuration').val('custom');
        $('#btnSubmitBulk').prop('disabled', true);
    }
    const item = {
        btn: $btn,
        x,
        y,
        status,
        code,
    }
    setDataFormAssign([id], [item], !plot.current_assignment);
}
// ===== Bulk Modal =====
const $b_startAt = $('#b_start_at'), $b_endAt = $('#b_end_at'), $b_quick = $('#b_quickDuration');
const $b_count = $('#bulkCount'), $b_summary = $('#bulkSummary'), $b_list = $('#bulkList'), $b_idsWrap = $('#bulkPlotIdsContainer');

$('#btnBulkAssign').on('click', function () {
    const ids = Array.from(selectedMap.keys()), items = Array.from(selectedMap.values());
    setDataFormAssign(ids, items, true);
});

function setDataFormAssign(ids, items, setDate = false) {
    $b_count.text(ids.length);
    $b_summary.html(trans('planting_cell_selected', { count: ids.length }));
    const cells = items.sort((a, b) => (a.y - b.y) || (a.x - b.x)).map(it => `<span class="badge badge-light">${it.code}</span>`).join(' ');
    $b_list.html(cells);
    $b_idsWrap.html(ids.map(id => `<input type="hidden" name="plot_ids[]" value="${id}">`).join(''));

    if (setDate) {
        const today = new Date();
        $b_startAt.val(moment(today).format(formatDate)); $b_quick.val('12');
        $b_endAt.val(addMonthsKeepEnd($b_startAt.val(), 12)); $b_endAt.attr('min', $b_startAt.val());
    }
    $('#bulkModal').modal('show');
    $('[data-picker="date"]').datepicker('update');//refresh datepicker inputs.
}

//reset form on modal closed
$('#bulkModal').on('hidden.bs.modal', function () {
    $(this).find('#bulkForm')[0].reset();
    $(this).find('select').val(null).trigger('change');
    $('#btnSubmitBulk').prop('disabled', false);
});

$b_startAt.on('change', function () { if ($b_quick.val() !== 'custom' && $(this).val()) { const m = parseInt($b_quick.val(), 10); $b_endAt.val(addMonthsKeepEnd($b_startAt.val(), m)); $b_endAt.attr('min', $b_startAt.val()); } else { $b_endAt.attr('min', $b_startAt.val()); } });
$b_quick.on('change', function () { if ($b_quick.val() === 'custom') return; const m = parseInt($b_quick.val(), 10); if ($b_startAt.val()) $b_endAt.val(addMonthsKeepEnd($b_startAt.val(), m)); });


//submit form
$('#bulkForm').on('submit', function (e) {
    e.preventDefault();
    const form = $(this);
    const buttonSubmit = $('#btnSubmitBulk');
    const formData = getFormData(this);
    buttonSubmit.prop('disabled', true);
    $.ajax({
        url: form.attr('action'),
        type: form.attr('method'),
        data: formData,
        success: function (res) {
            $('#bulkModal').modal('hide');
            toastr.success(res.message);
            setTimeout(() => location.reload(), 2500);
        },
        error: function (xhr) {
            const { responseJSON, status } = xhr;
            if (status === 422) {
                const { errors } = responseJSON;
                let isHasFirst = false;
                Object.keys(errors).forEach(field => {
                    const input = form.find(`[name="${field}"]`);
                    if (!isHasFirst) {
                        input[0].focus();
                        isHasFirst = true;
                    }

                    // Find error span: Select2 uses parent form-group, regular inputs use next()
                    const errorSpan = input.hasClass('select2-hidden-accessible')
                        ? input.closest('.form-group').parent().find('.text-danger')
                        : input.next();
                    errorSpan.text(errors[field][0]);
                });
                return;
            }
            toastr.error(responseJSON.message);
        },
        complete: () => {
            buttonSubmit.prop('disabled', false);
        }
    });
});
