<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver; // or Imagick if available

class AvatarService
{
    private const AVATAR_SiZES = [
        'xl' => 512,
        'lg' => 256,
        'md' => 128,
        'sm' => 64,   // path in DB always points to this
    ];
    public const DEFAULT_SIZE = 'sm';

    protected ImageManager $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Process avatar upload:
     * - Create new UUID folder
     * - Resize to all sizes
     * - Return path of sm.jpg to save in DB
     */
    public function updateUserAvatar(User $user, string $filePath): string
    {
        $uuid = (string) Str::uuid();
        $baseDir = "avatars/{$uuid}";

        // Load source image
        $src = $this->imageManager->read($filePath)->orient();
        $srcW = $src->width();
        $srcH = $src->height();
        $srcMin = min($srcW, $srcH);

        // Generate and save each size
        foreach (self::AVATAR_SiZES as $key => $px) {
            $target = min($px, $srcMin); // Don't upscale small images
            $clone = $src->cover($target, $target);  // crop center & resize square
            $binary = $clone->toWebp(85);
            Storage::put("{$baseDir}/{$key}.webp", $binary);
        }

        return "{$baseDir}/sm.webp";
    }

    /**
     * Get path for a different size based on sm path stored in DB.
     */
    public static function pathForSize(string $smPath, string $size): string
    {
        $dir = dirname($smPath);
        return "{$dir}/{$size}.webp";
    }

    /**
     * Return allowed sizes (for validation).
     */
    public static function allowedSizes(): array
    {
        return array_keys(self::AVATAR_SiZES);
    }
}
