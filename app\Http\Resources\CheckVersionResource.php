<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CheckVersionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /**
             * Current version of the app.
             * @var string
             * @example 1.0.0
             */
            'current_version' => $this->current_version,
            /**
             * Latest version of the app.
             * @var string
             * @example 1.0.1
             */
            'latest_version' => $this->latest_version,
            /**
             * Is there a new version available.
             * @var boolean
             * @example true
             */
            'has_new_version' => $this->has_new_version,
            /**
             * Is force update required.
             * @var boolean
             * @example true
             */
            'force_update' => $this->force_update,
            /**
             * Mode of the app (1: production, 2: preview).
             * @var integer
             * @example 1
             */
            'mode' => $this->mode,
            /**
             * Published at date of the latest version.
             * @var string
             * @example 2025-01-01 00:00:00
             */
            'published_at' => $this->published_at,
            /**
             * Description of the latest version.
             * @var string
             * @example "There is a new version available. Please update."
             */
            'description' => $this->description
        ];
    }
}
