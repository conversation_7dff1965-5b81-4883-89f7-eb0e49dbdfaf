<?php

namespace App\Providers;

use App\Exceptions\ExceptionHandler;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Collection;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(ExceptionHandler::class, function ($app) {
            return new ExceptionHandler();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Macro Collection Append
        Collection::macro('withAppends', function ($appends = []) {
            $appends = is_array($appends) ? $appends : func_get_args();
            return $this->each->setAppends($appends);
        });
    }
}
