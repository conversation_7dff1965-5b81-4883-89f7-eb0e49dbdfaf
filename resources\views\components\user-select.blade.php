@props([
    'name' => 'user',
    'placeholder' => null
])

@php
    // Set default values
    $placeholder = $placeholder ?? trans('language.customer');
@endphp

<div class="form-group">
    <select class="select2-data-users select2-hidden-accessible"
            data-url="{{ route('getListUser') }}"
            name="{{ $name }}"
            data-placeholder="{{ $placeholder }}"
            style="width: 100%">
        @if(isset(request()->user))
            @php
                $member = \App\Models\User::where('id', request()->user)->select(['id', 'name', 'email', 'phone'])->first();
            @endphp
            @if($member)
                <option value="{{ $member->id }}" selected>{{ $member->name . ' | ' . $member->email . ' | ' . $member->phone }}</option>
            @endif
        @endif
    </select>
</div>
