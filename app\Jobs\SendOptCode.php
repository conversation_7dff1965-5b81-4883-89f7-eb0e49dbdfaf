<?php

namespace App\Jobs;

use App\Enums\IdentifierType;
use App\Helpers\StringHelper;
use App\Mail\MailSendCode;
use App\Services\SmsService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class SendOptCode implements ShouldQueue
{
    use Queueable;
    public $identifier;
    public $code;
    public $subject;
    public $view;
    /**
     * Create a new job instance.
     */
    public function __construct($identifier, $code, $subject, $view)
    {
        $this->identifier = $identifier;
        $this->code = $code;
        $this->subject = $subject;
        $this->view = $view;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ((new StringHelper)->detectIdentifierType($this->identifier) == IdentifierType::EMAIL->value) {
            $content = new MailSendCode($this->code, $this->subject, $this->view);
            Mail::to($this->identifier)->send($content);
        } else {
            $smsService = new SmsService();
            $smsService->sendOtp($this->code, [$this->identifier]);
        }
    }
}
