<?php

namespace App\Http\Controllers;

use App\Helpers\FilterHelper;
use App\Helpers\StringHelper;
use App\Enums\FilterType;
use App\Enums\OrderStatus;
use App\Enums\OrderType;
use App\Enums\PaymentChannel;
use App\Logics\OrderManager;
use App\Models\Order;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    private $filterHelper;
    private $orderManager;

    public function __construct(FilterHelper $filterHelper, OrderManager $orderManager)
    {
        $this->filterHelper = $filterHelper;
        $this->orderManager = $orderManager;
    }

    /**
     * Display a listing of orders
     */
    public function index(Request $request)
    {
        // Get orders with filters using OrderManager
        $orders = $this->orderManager->getOrderList($request);

        // Pagination - 20 records per page by default
        $perPage = $request->has('per_page') ? $request->input('per_page') : PER_PAGE;
        $orders = $orders->paginate($perPage);

        if ($redirect = $this->redirectInvalidPage($orders, $request)) {
            return $redirect;
        }

        // Get filter configuration and render badges
        $filterConfig = $this->getFilterConfig();
        $isFilter = $this->filterHelper->renderFilterBadges($request, $filterConfig);

        return view('order.index', [
            'orders' => $orders,
            'is_filter' => $isFilter,
        ]);
    }

    /**
     * Get filter configuration for orders
     * @return array
     */
    private function getFilterConfig(): array
    {
        return [
            FilterType::createConfig('code', FilterType::TEXT, [

            ]),
            FilterType::createConfig('type', FilterType::ARRAY, [
                'mapping' => array_combine(
                    array_column(OrderType::cases(), 'value'),
                    array_map(fn($case) => $case->label(), OrderType::cases())
                )
            ]),
            FilterType::createConfig('user', FilterType::FIELD_FROM_DATABASE, [
                'table' => User::class,
                'field' => 'name',
            ]),
            FilterType::createConfig('status', FilterType::ARRAY, [
                'mapping' => array_combine(
                    array_column(OrderStatus::cases(), 'value'),
                    array_map(fn($case) => $case->label(), OrderStatus::cases())
                )
            ]),
            FilterType::createConfig('name', FilterType::TEXT, [
            ]),
            FilterType::createConfig('channel', FilterType::ARRAY, [
                'mapping' => array_combine(
                    array_column(PaymentChannel::cases(), 'value'),
                    array_map(fn($case) => $case->label(), PaymentChannel::cases())
                )
            ]),
            FilterType::createConfig('from', FilterType::DATE, []),
            FilterType::createConfig('to', FilterType::DATE, []),
            FilterType::createConfig('from_completed', FilterType::DATE, []),
            FilterType::createConfig('to_completed', FilterType::DATE, []),
        ];
    }

}
