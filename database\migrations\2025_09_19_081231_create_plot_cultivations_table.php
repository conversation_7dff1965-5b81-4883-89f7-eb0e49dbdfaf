<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plot_cultivations', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('plot_id');              // target plot
            $table->unsignedBigInteger('plant_id')->nullable(); // planted crop (optional)

            // Cultivation window. end_at = NULL means the cultivation is still active
            $table->date('start_at');
            $table->date('end_at')->nullable();

            // Current state & stage stored as flexible JSON blobs
            // Example: current_state => {"status":"growing","note":"..."}
            //          current_stage => {"id":12,"name":"Vegetative","order":2}
            $table->json('current_state')->nullable();
            $table->json('current_stage')->nullable();

            $table->timestamps();

            // Indexes for common queries
            $table->index('plot_id');
            $table->index('plant_id');
            $table->index('end_at');

            // Fast lookups: active (end_at IS NULL) and chronological history
            $table->index(['plot_id', 'end_at']);
            $table->index(['plot_id', 'start_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plot_cultivations');
    }
};
