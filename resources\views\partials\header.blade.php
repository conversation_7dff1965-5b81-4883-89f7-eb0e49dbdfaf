@php
    $user = auth()->user();
@endphp
<nav class="main-header navbar navbar-expand border-bottom-0">
    <ul class="navbar-nav">
        <li class="nav-item">
            <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
        @yield('header')
    </ul>
    <ul class="navbar-nav ml-auto">
        <li class="nav-item dropdown">
            <a class="nav-link" data-toggle="dropdown" href="#">
                <img src="{{ $user->avatarUrl() }}" class="size-46 img-circle img-avatar">
            </a>
            <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right mt-2 border-radius-12">
                <div class="dropdown-header">
                    <span>
                        {{ $user->name }}{{ __('language.san') }}
                    </span>
                    <span class="close">
                        <img src="{{ asset('images/nav/close.svg') }}" />
                    </span>
                </div>
                <div class="dropdown-menu-content">
                    <a @if (Route::has('user.public')) href="{{ route('user.public', ['id' => $user->id]) }}" @endif
                        class="dropdown-item">
                        <div class="form-image">
                            <img src="{{ $user->avatarUrl('md') }}"
                                class="size-80 img-circle img-avatar">
                            {{-- @if (Route::has('users.edit')) <a href="{{ route('user.edit') }}" class="form-image__label"><i class="far fa-pen"></i></a> @endif --}}
                        </div>
                        <div class="media-body">
                            <p class="blue dropdown-item-title">
                                {{ $user->email }}
                            </p>
                        </div>
                    </a>
                </div>
                <div class="dropdown-user-list">
                    <ul>
                        <li>
                            <a href="{{ route('user.edit', ['user' => $user]) }}">
                                <img class="icon-nav__item" src="{{ asset('images/nav/edit_profile.svg') }}">
                                {{ trans('language.edit_profile') }}
                            </a>
                        </li>
                        <li>
                            <a href="javascript:void(0)" data-toggle="modal" data-target="#modalChangePassword">
                                <img class="icon-nav__item" src="{{ asset('images/nav/password.svg') }}">
                                {{ trans('language.change_password') }}
                            </a>
                        </li>
                        <li>
                            <a href="javascript:void(0)" id="logout">
                                <img class="icon-nav__item" src="{{ asset('images/nav/logout.svg') }}">
                                {{ trans('language.logout') }}
                            </a>
                            <form id="logout-form" action="{{ route('logout') }}" method="POST">
                                @csrf
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </li>
    </ul>
</nav>
