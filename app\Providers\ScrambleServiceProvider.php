<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Dedoc\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Dedoc\Scramble\Support\Generator\Parameter;
use Dedoc\Scramble\Support\Generator\Schema;
use Dedoc\Scramble\Support\Generator\Types\StringType;

class ScrambleServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $isInstalled = class_exists(Scramble::class) && class_exists(OpenApi::class) && class_exists(SecurityScheme::class);
        if ($isInstalled) {
            Scramble::configure()
                ->withOperationTransformers(function ($operation) {
                    $operation->addParameters([
                        Parameter::make('Accept-Language', 'header')
                            ->setSchema(Schema::fromType(new StringType()))
                            ->required(false)
                            ->example('vi')
                            ->description('Language preference for localized responses (vi, en, etc.)')
                    ]);
                })
                ->withDocumentTransformers(function (OpenApi $openApi) {
                    $openApi->secure(
                        SecurityScheme::http('bearer')
                    );
                });
        }
    }
}
