.seat-grid {
    display: grid;
    grid-auto-rows: 44px;
    column-gap: 6px;
    row-gap: 6px;
    align-items: stretch;
}

.seat-row-label,
.seat-col-label,
.seat {
    height: 100% !important;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
}

.seat {
    width: 42px;
}

.seat.btn {
    padding: 0 !important;
    line-height: 1 !important;
}

.seat-picked {
    position: relative;
    box-shadow: 0 0 0 3px rgba(255, 193, 7, .6);
}

.seat-picked::after {
    content: "";
    position: absolute;
    inset: 0;
    border: 2px dashed #ffc107;
    border-radius: .5rem;
    pointer-events: none;
}

#rubberBand {
    position: absolute;
    border: 1px dashed #17a2b8;
    background: rgba(23, 162, 184, .15);
    display: none;
    pointer-events: none;
    z-index: 10;
}

/* Tooltip align left */
.tooltip .tooltip-inner {
    text-align: left !important;
    max-width: 360px;
    white-space: normal;
}

.plan-grid-page {
    .card-header::after {
        content: none;
    }
    #gridWrap {
        overflow-x: auto;
    }
}

#bulkList {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}