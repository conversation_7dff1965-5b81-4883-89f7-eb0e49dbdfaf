{{-- cropGroupOptions is passed from controller --}}

<div class="d-flex justify-content-between align-items-end flex-wrap">
    <div class="mr-1 mb-2">
        <a href="{{ route('crops.index', ['refresh' => 'true']) }}"
            class="btn btn-default text-white d-none d-sm-inline-block">
            <i class="fal fa-sync"></i> {{ __('language.refresh') }}
        </a>
        <a href="#card-filter" class="btn btn-default text-white" data-toggle="collapse">
            <i class="far fa-filter"></i> {{ __('language.filter') }}
        </a>
    </div>
    <div class="actions mb-2">
        <a href="{{ route('crops.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {{ __('language.add_new') }}
        </a>
    </div>
</div>

<div class="collapse" id="card-filter">
    <div class="card mb-3">
        <div class="card-body border-0">
            <form action="{{ route('crops.index') }}" method="GET">
                <div class="row">
                    <div class="col-md-6 col-xl-4 mb-3">
                        <input class="form-control" type="text" name="keyword" value="{{ request('keyword') }}"
                            placeholder="{{ __('language.by_crop_name') }}">
                    </div>

                    <!-- Crop Group Filter -->
                    <div class="col-md-6 col-xl-4 mb-3">
                        <select name="crop_group_id" class="form-control select2-base">
                            <option value="">{{ __('language.by_crop_group') }}</option>
                            @foreach($cropGroupOptions as $id => $name)
                                <option value="{{ $id }}" {{ request('crop_group_id') == $id ? 'selected' : '' }}>
                                    {{ $name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Filter Button -->
                    <div class="col-12 text-right">
                        <button type="submit" class="btn btn-primary button-filter" style="width: 158px">
                            <i class="fas fa-filter"></i> {{ __('language.filter') }}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>