<form action="">
    <div class="row">
        <!-- Row 1 -->
        <div class="col-sm-3 col-md-3 col-xl-3 mb-3">
            <input type="text" class="form-control" placeholder="{{ trans('language.order_code') }}" name="code"
                @if ($request->has('code')) value="{{ $request->code }}" @endif>
        </div>
        <div class="col-sm-3 col-md-3 col-xl-3 mb-3">
            <select class="select2-base select2-hidden-accessible" style="width: 100%" data-placeholder="{{ trans('language.order_type') }}" name="type[]"
                multiple="multiple">
                <option value=""></option>
                @foreach(\App\Enums\OrderType::cases() as $orderType)
                    <option value="{{ $orderType->value }}" @if (
                        $request->has('type') && in_array($orderType->value, $request->type ?? [])
                        && $request->type != null
                    ) selected @endif>
                        {{ $orderType->label() }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-sm-3 col-md-3 col-xl-3 mb-3">
            <x-user-select name="user" :placeholder="trans('language.customer')" />
        </div>
        <div class="col-sm-3 col-md-3 col-xl-3 mb-3">
            <select class="select2-base select2-hidden-accessible" style="width: 100%" data-placeholder="{{ trans('language.status') }}" name="status[]"
                multiple="multiple">
                <option value=""></option>
                @foreach(\App\Enums\OrderStatus::cases() as $orderStatus)
                    <option value="{{ $orderStatus->value }}" @if (
                        $request->has('status') && in_array($orderStatus->value, $request->status ?? [])
                        && $request->status != null
                    ) selected @endif>
                        {{ $orderStatus->label() }}</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="row">
        <!-- Row 2 -->
        <div class="col-sm-3 col-md-3 col-xl-3 mb-3">
            <label class="input-group mb-0">
                <input type="text" class="form-control" placeholder="{{ trans('language.order_content') }}" name="name"
                    @if ($request->has('name')) value="{{ $request->name }}" @endif>
            </label>
        </div>
        <div class="col-sm-3 col-md-3 col-xl-3 mb-3">
            <select class="select2-base select2-hidden-accessible" style="width: 100%" data-placeholder="{{ trans('language.payment_channel') }}" name="channel[]"
                multiple="multiple">
                <option value=""></option>
                @foreach(\App\Enums\PaymentChannel::cases() as $paymentChannel)
                    <option value="{{ $paymentChannel->value }}" @if (
                        $request->has('channel') && in_array($paymentChannel->value, $request->channel ?? [])
                        && $request->channel != null
                    ) selected @endif>
                        {{ $paymentChannel->label() }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-sm-6 col-md-6 mb-3">
            <div class="row" data-picker="rangeDate">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="input-group">
                            <input type="text" class="form-control start_at" name="from"
                                   placeholder="{{ trans('language.created_date_from') }}" autocomplete="off"
                                   value="{{$request->from ? \App\Helpers\DateTimeHelper::formatLanguage($request->from,'date') : ''}}">
                            <div class="input-group-append">
                                <span class="input-group-text"><img src="{{ asset('images/icon-calendar.svg') }}" /></span>
                            </div>
                        </label>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="input-group">
                            <input type="text" class="form-control end_at" name="to"
                                   placeholder="{{ trans('language.created_date_to') }}" autocomplete="off"
                                   value="{{$request->to ? \App\Helpers\DateTimeHelper::formatLanguage($request->to,'date') : ''}}">
                            <div class="input-group-append">
                                <span class="input-group-text"><img src="{{ asset('images/icon-calendar.svg') }}" /></span>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <!-- Row 3 -->
        <div class="col-sm-6 col-md-6 mb-3">
            <div class="row" data-picker="rangeDate">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="input-group">
                            <input type="text" class="form-control start_at" name="from_completed"
                                   placeholder="{{ trans('language.completed_date_from') }}" autocomplete="off"
                                   value="{{$request->from_completed ? \App\Helpers\DateTimeHelper::formatLanguage($request->from_completed,'date') : ''}}">
                            <div class="input-group-append">
                                <span class="input-group-text"><img src="{{ asset('images/icon-calendar.svg') }}" /></span>
                            </div>
                        </label>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="input-group">
                            <input type="text" class="form-control end_at" name="to_completed"
                                   placeholder="{{ trans('language.completed_date_to') }}" autocomplete="off"
                                   value="{{$request->to_completed ? \App\Helpers\DateTimeHelper::formatLanguage($request->to_completed,'date') : ''}}">
                            <div class="input-group-append">
                                <span class="input-group-text"><img src="{{ asset('images/icon-calendar.svg') }}" /></span>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 col-md-12 mb-3 text-right">
            <button type="submit" class="btn btn-primary button-filter"><i class="fas fa-filter"></i>
                {{ trans('language.filter') }}</button>
        </div>
    </div>

</form>



